-- =============================================
-- MediEase Stored Procedures
-- Optimized procedures for common operations
-- =============================================

-- =============================================
-- 1. USER MANAGEMENT PROCEDURES
-- =============================================

-- Procedure: Register New User
CREATE PROCEDURE sp_RegisterUser
    @Email NVARCHAR(100),
    @PasswordHash NVARCHAR(255),
    @FirstName NVARCHAR(50),
    @LastName NVARCHAR(50),
    @PhoneNumber NVARCHAR(20) = NULL,
    @Role NVARCHAR(20) = 'Customer'
AS
BEGIN
    SET NOCOUNT ON;
    
    -- Check if email already exists
    IF EXISTS (SELECT 1 FROM Users WHERE Email = @Email)
    BEGIN
        RAISERROR('Email already exists', 16, 1);
        RETURN;
    END
    
    -- Insert new user
    INSERT INTO Users (Email, PasswordHash, FirstName, LastName, PhoneNumber, Role, 
                      IsActive, IsEmailVerified, CreatedDate)
    VALUES (@Email, @PasswordHash, @FirstName, @LastName, @PhoneNumber, @Role, 
            1, 0, GETDATE());
    
    -- Return new user ID
    SELECT SCOPE_IDENTITY() as UserId;
END;
GO

-- Procedure: Authenticate User
CREATE PROCEDURE sp_AuthenticateUser
    @Email NVARCHAR(100),
    @PasswordHash NVARCHAR(255)
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT UserId, Email, FirstName, LastName, Role, IsActive, IsEmailVerified, LoyaltyPoints
    FROM Users 
    WHERE Email = @Email AND PasswordHash = @PasswordHash AND IsActive = 1;
    
    -- Update last login if user found
    IF @@ROWCOUNT > 0
    BEGIN
        UPDATE Users 
        SET LastLoginDate = GETDATE() 
        WHERE Email = @Email;
    END
END;
GO

-- =============================================
-- 2. SHOPPING CART PROCEDURES
-- =============================================

-- Procedure: Add Item to Cart
CREATE PROCEDURE sp_AddToCart
    @UserId INT,
    @MedicineId INT,
    @Quantity INT
AS
BEGIN
    SET NOCOUNT ON;
    
    -- Check if medicine exists and is active
    IF NOT EXISTS (SELECT 1 FROM Medicines WHERE MedicineId = @MedicineId AND IsActive = 1)
    BEGIN
        RAISERROR('Medicine not found or inactive', 16, 1);
        RETURN;
    END
    
    -- Check stock availability
    DECLARE @StockQuantity INT;
    SELECT @StockQuantity = StockQuantity FROM Medicines WHERE MedicineId = @MedicineId;
    
    IF @StockQuantity < @Quantity
    BEGIN
        RAISERROR('Insufficient stock', 16, 1);
        RETURN;
    END
    
    -- Add or update cart item
    IF EXISTS (SELECT 1 FROM ShoppingCart WHERE UserId = @UserId AND MedicineId = @MedicineId)
    BEGIN
        UPDATE ShoppingCart 
        SET Quantity = Quantity + @Quantity, ModifiedDate = GETDATE()
        WHERE UserId = @UserId AND MedicineId = @MedicineId;
    END
    ELSE
    BEGIN
        INSERT INTO ShoppingCart (UserId, MedicineId, Quantity, AddedDate)
        VALUES (@UserId, @MedicineId, @Quantity, GETDATE());
    END
    
    -- Return cart summary
    SELECT COUNT(*) as ItemCount, 
           SUM(sc.Quantity * m.Price * (1 - m.DiscountPercentage / 100)) as TotalAmount
    FROM ShoppingCart sc
    INNER JOIN Medicines m ON sc.MedicineId = m.MedicineId
    WHERE sc.UserId = @UserId;
END;
GO

-- =============================================
-- 3. ORDER PROCESSING PROCEDURES
-- =============================================

-- Procedure: Create Order from Cart
CREATE PROCEDURE sp_CreateOrderFromCart
    @UserId INT,
    @ShippingAddress NVARCHAR(500),
    @ShippingCity NVARCHAR(100),
    @ShippingPostalCode NVARCHAR(20),
    @ContactPhone NVARCHAR(20),
    @ContactEmail NVARCHAR(100),
    @PaymentMethod NVARCHAR(50),
    @TaxRate DECIMAL(5,2) = 8.5,
    @ShippingCost DECIMAL(10,2) = 5.99
AS
BEGIN
    SET NOCOUNT ON;
    BEGIN TRANSACTION;
    
    DECLARE @OrderId INT;
    DECLARE @OrderNumber NVARCHAR(50);
    DECLARE @Subtotal DECIMAL(10,2);
    DECLARE @TaxAmount DECIMAL(10,2);
    DECLARE @TotalAmount DECIMAL(10,2);
    DECLARE @RequiresPrescription BIT = 0;
    
    -- Generate order number
    SET @OrderNumber = 'ORD' + FORMAT(GETDATE(), 'yyyyMMdd') + FORMAT(NEXT VALUE FOR OrderNumberSequence, '0000');
    
    -- Calculate totals
    SELECT @Subtotal = SUM(sc.Quantity * m.Price * (1 - m.DiscountPercentage / 100)),
           @RequiresPrescription = MAX(CAST(m.PrescriptionRequired as INT))
    FROM ShoppingCart sc
    INNER JOIN Medicines m ON sc.MedicineId = m.MedicineId
    WHERE sc.UserId = @UserId;
    
    IF @Subtotal IS NULL
    BEGIN
        ROLLBACK TRANSACTION;
        RAISERROR('Cart is empty', 16, 1);
        RETURN;
    END
    
    SET @TaxAmount = @Subtotal * (@TaxRate / 100);
    SET @TotalAmount = @Subtotal + @TaxAmount + @ShippingCost;
    
    -- Create order
    INSERT INTO Orders (OrderNumber, CustomerId, Status, OrderDate, Subtotal, TaxAmount,
                       ShippingCost, TotalAmount, PaymentMethod, PaymentStatus,
                       ShippingAddress, ShippingCity, ShippingPostalCode, ContactPhone,
                       ContactEmail, RequiresPrescription, CreatedDate)
    VALUES (@OrderNumber, @UserId, 'Pending', GETDATE(), @Subtotal, @TaxAmount,
            @ShippingCost, @TotalAmount, @PaymentMethod, 'Pending',
            @ShippingAddress, @ShippingCity, @ShippingPostalCode, @ContactPhone,
            @ContactEmail, @RequiresPrescription, GETDATE());
    
    SET @OrderId = SCOPE_IDENTITY();
    
    -- Create order items from cart
    INSERT INTO OrderItems (OrderId, MedicineId, Quantity, UnitPrice, TotalPrice, CreatedDate)
    SELECT @OrderId, sc.MedicineId, sc.Quantity, 
           m.Price * (1 - m.DiscountPercentage / 100),
           sc.Quantity * m.Price * (1 - m.DiscountPercentage / 100),
           GETDATE()
    FROM ShoppingCart sc
    INNER JOIN Medicines m ON sc.MedicineId = m.MedicineId
    WHERE sc.UserId = @UserId;
    
    -- Update stock quantities
    UPDATE m
    SET StockQuantity = m.StockQuantity - sc.Quantity,
        PurchaseCount = m.PurchaseCount + sc.Quantity
    FROM Medicines m
    INNER JOIN ShoppingCart sc ON m.MedicineId = sc.MedicineId
    WHERE sc.UserId = @UserId;
    
    -- Clear cart
    DELETE FROM ShoppingCart WHERE UserId = @UserId;
    
    -- Add loyalty points (1 point per dollar)
    DECLARE @LoyaltyPoints INT = CAST(@TotalAmount as INT);
    UPDATE Users 
    SET LoyaltyPoints = LoyaltyPoints + @LoyaltyPoints
    WHERE UserId = @UserId;
    
    -- Record loyalty transaction
    INSERT INTO LoyaltyTransactions (UserId, OrderId, TransactionType, Points, Description, CreatedDate)
    VALUES (@UserId, @OrderId, 'EARNED', @LoyaltyPoints, 'Points earned from order #' + @OrderNumber, GETDATE());
    
    COMMIT TRANSACTION;
    
    -- Return order details
    SELECT @OrderId as OrderId, @OrderNumber as OrderNumber, @TotalAmount as TotalAmount;
END;
GO

-- =============================================
-- 4. INVENTORY MANAGEMENT PROCEDURES
-- =============================================

-- Procedure: Update Stock with Movement Tracking
CREATE PROCEDURE sp_UpdateStock
    @MedicineId INT,
    @Quantity INT,
    @MovementType NVARCHAR(20), -- 'IN', 'OUT', 'ADJUSTMENT'
    @Reference NVARCHAR(100) = NULL,
    @Notes NVARCHAR(500) = NULL,
    @UserId INT = NULL
AS
BEGIN
    SET NOCOUNT ON;
    BEGIN TRANSACTION;
    
    DECLARE @CurrentStock INT;
    DECLARE @NewStock INT;
    
    -- Get current stock
    SELECT @CurrentStock = StockQuantity FROM Medicines WHERE MedicineId = @MedicineId;
    
    IF @CurrentStock IS NULL
    BEGIN
        ROLLBACK TRANSACTION;
        RAISERROR('Medicine not found', 16, 1);
        RETURN;
    END
    
    -- Calculate new stock
    IF @MovementType = 'IN'
        SET @NewStock = @CurrentStock + @Quantity;
    ELSE IF @MovementType = 'OUT'
        SET @NewStock = @CurrentStock - @Quantity;
    ELSE IF @MovementType = 'ADJUSTMENT'
        SET @NewStock = @Quantity;
    ELSE
    BEGIN
        ROLLBACK TRANSACTION;
        RAISERROR('Invalid movement type', 16, 1);
        RETURN;
    END
    
    -- Ensure stock doesn't go negative
    IF @NewStock < 0
        SET @NewStock = 0;
    
    -- Update medicine stock
    UPDATE Medicines 
    SET StockQuantity = @NewStock, ModifiedDate = GETDATE()
    WHERE MedicineId = @MedicineId;
    
    -- Record stock movement
    INSERT INTO StockMovements (MedicineId, MovementType, Quantity, PreviousStock, NewStock,
                               Reference, Notes, CreatedDate, CreatedBy)
    VALUES (@MedicineId, @MovementType, @Quantity, @CurrentStock, @NewStock,
            @Reference, @Notes, GETDATE(), @UserId);
    
    COMMIT TRANSACTION;
    
    -- Return new stock level
    SELECT @NewStock as NewStockLevel;
END;
GO

-- =============================================
-- 5. PRESCRIPTION PROCESSING PROCEDURES
-- =============================================

-- Procedure: Process Prescription Upload
CREATE PROCEDURE sp_ProcessPrescription
    @UserId INT,
    @DoctorName NVARCHAR(100),
    @DoctorLicense NVARCHAR(50) = NULL,
    @PrescriptionDate DATE,
    @ExpiryDate DATE = NULL,
    @OriginalFileName NVARCHAR(255),
    @FilePath NVARCHAR(500),
    @FileSize BIGINT,
    @FileType NVARCHAR(50)
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @PrescriptionNumber NVARCHAR(50);
    DECLARE @PrescriptionId INT;
    
    -- Generate prescription number
    SET @PrescriptionNumber = 'RX' + FORMAT(GETDATE(), 'yyyyMMdd') + FORMAT(NEXT VALUE FOR PrescriptionNumberSequence, '0000');
    
    -- Insert prescription
    INSERT INTO Prescriptions (UserId, PrescriptionNumber, DoctorName, DoctorLicense,
                              PrescriptionDate, ExpiryDate, OriginalFileName, FilePath,
                              FileSize, FileType, Status, CreatedDate)
    VALUES (@UserId, @PrescriptionNumber, @DoctorName, @DoctorLicense,
            @PrescriptionDate, @ExpiryDate, @OriginalFileName, @FilePath,
            @FileSize, @FileType, 'Pending', GETDATE());
    
    SET @PrescriptionId = SCOPE_IDENTITY();
    
    -- Create notification for pharmacists
    INSERT INTO Notifications (UserId, Title, Message, NotificationType, RelatedEntityType,
                              RelatedEntityId, Priority, CreatedDate)
    SELECT UserId, 'New Prescription Uploaded', 
           'A new prescription #' + @PrescriptionNumber + ' requires verification',
           'PRESCRIPTION_UPLOAD', 'Prescription', @PrescriptionId, 'High', GETDATE()
    FROM Users 
    WHERE Role = 'Pharmacist' AND IsActive = 1;
    
    -- Return prescription details
    SELECT @PrescriptionId as PrescriptionId, @PrescriptionNumber as PrescriptionNumber;
END;
GO

-- =============================================
-- 6. REPORTING PROCEDURES
-- =============================================

-- Procedure: Generate Sales Report
CREATE PROCEDURE sp_GenerateSalesReport
    @StartDate DATE,
    @EndDate DATE,
    @GroupBy NVARCHAR(20) = 'DAY' -- 'DAY', 'WEEK', 'MONTH'
AS
BEGIN
    SET NOCOUNT ON;
    
    IF @GroupBy = 'DAY'
    BEGIN
        SELECT 
            CAST(OrderDate as DATE) as Period,
            COUNT(*) as TotalOrders,
            SUM(TotalAmount) as TotalRevenue,
            AVG(TotalAmount) as AverageOrderValue,
            COUNT(DISTINCT CustomerId) as UniqueCustomers
        FROM Orders 
        WHERE Status NOT IN ('Cancelled') 
          AND OrderDate >= @StartDate AND OrderDate <= @EndDate
        GROUP BY CAST(OrderDate as DATE)
        ORDER BY Period;
    END
    ELSE IF @GroupBy = 'WEEK'
    BEGIN
        SELECT 
            DATEPART(YEAR, OrderDate) as Year,
            DATEPART(WEEK, OrderDate) as Week,
            MIN(CAST(OrderDate as DATE)) as WeekStart,
            MAX(CAST(OrderDate as DATE)) as WeekEnd,
            COUNT(*) as TotalOrders,
            SUM(TotalAmount) as TotalRevenue,
            AVG(TotalAmount) as AverageOrderValue,
            COUNT(DISTINCT CustomerId) as UniqueCustomers
        FROM Orders 
        WHERE Status NOT IN ('Cancelled') 
          AND OrderDate >= @StartDate AND OrderDate <= @EndDate
        GROUP BY DATEPART(YEAR, OrderDate), DATEPART(WEEK, OrderDate)
        ORDER BY Year, Week;
    END
    ELSE IF @GroupBy = 'MONTH'
    BEGIN
        SELECT 
            YEAR(OrderDate) as Year,
            MONTH(OrderDate) as Month,
            DATENAME(MONTH, OrderDate) as MonthName,
            COUNT(*) as TotalOrders,
            SUM(TotalAmount) as TotalRevenue,
            AVG(TotalAmount) as AverageOrderValue,
            COUNT(DISTINCT CustomerId) as UniqueCustomers
        FROM Orders 
        WHERE Status NOT IN ('Cancelled') 
          AND OrderDate >= @StartDate AND OrderDate <= @EndDate
        GROUP BY YEAR(OrderDate), MONTH(OrderDate), DATENAME(MONTH, OrderDate)
        ORDER BY Year, Month;
    END
END;
GO

-- =============================================
-- 7. MAINTENANCE PROCEDURES
-- =============================================

-- Procedure: Clean Old Data
CREATE PROCEDURE sp_CleanOldData
    @DaysToKeep INT = 365
AS
BEGIN
    SET NOCOUNT ON;
    BEGIN TRANSACTION;
    
    DECLARE @CutoffDate DATETIME = DATEADD(DAY, -@DaysToKeep, GETDATE());
    DECLARE @DeletedCount INT;
    
    -- Clean old error logs
    DELETE FROM ErrorLogs WHERE CreatedDate < @CutoffDate;
    SET @DeletedCount = @@ROWCOUNT;
    PRINT 'Deleted ' + CAST(@DeletedCount as NVARCHAR) + ' old error logs';
    
    -- Clean old audit logs
    DELETE FROM AuditLogs WHERE Timestamp < @CutoffDate;
    SET @DeletedCount = @@ROWCOUNT;
    PRINT 'Deleted ' + CAST(@DeletedCount as NVARCHAR) + ' old audit logs';
    
    -- Clean old chat sessions
    DELETE FROM ChatMessages 
    WHERE SessionId IN (
        SELECT SessionId FROM ChatSessions 
        WHERE StartDate < @CutoffDate AND IsActive = 0
    );
    
    DELETE FROM ChatSessions 
    WHERE StartDate < @CutoffDate AND IsActive = 0;
    SET @DeletedCount = @@ROWCOUNT;
    PRINT 'Deleted ' + CAST(@DeletedCount as NVARCHAR) + ' old chat sessions';
    
    -- Clean expired notifications
    DELETE FROM Notifications 
    WHERE ExpiryDate IS NOT NULL AND ExpiryDate < GETDATE();
    SET @DeletedCount = @@ROWCOUNT;
    PRINT 'Deleted ' + CAST(@DeletedCount as NVARCHAR) + ' expired notifications';
    
    COMMIT TRANSACTION;
    PRINT 'Data cleanup completed successfully';
END;
GO

-- =============================================
-- 8. UTILITY PROCEDURES
-- =============================================

-- Procedure: Get Dashboard Statistics
CREATE PROCEDURE sp_GetDashboardStats
    @UserId INT = NULL,
    @Role NVARCHAR(20) = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    IF @Role = 'Admin'
    BEGIN
        -- Admin dashboard stats
        SELECT 
            (SELECT COUNT(*) FROM Users WHERE Role = 'Customer' AND IsActive = 1) as TotalCustomers,
            (SELECT COUNT(*) FROM Orders WHERE Status NOT IN ('Cancelled')) as TotalOrders,
            (SELECT SUM(TotalAmount) FROM Orders WHERE Status NOT IN ('Cancelled')) as TotalRevenue,
            (SELECT COUNT(*) FROM Medicines WHERE IsActive = 1) as TotalMedicines,
            (SELECT COUNT(*) FROM Medicines WHERE StockQuantity <= MinStockLevel AND IsActive = 1) as LowStockItems,
            (SELECT COUNT(*) FROM Prescriptions WHERE Status = 'Pending') as PendingPrescriptions;
    END
    ELSE IF @Role = 'Pharmacist'
    BEGIN
        -- Pharmacist dashboard stats
        SELECT 
            (SELECT COUNT(*) FROM Orders WHERE Status = 'Processing') as OrdersToProcess,
            (SELECT COUNT(*) FROM Prescriptions WHERE Status = 'Pending') as PrescriptionsToVerify,
            (SELECT COUNT(*) FROM Medicines WHERE StockQuantity <= MinStockLevel AND IsActive = 1) as LowStockItems,
            (SELECT COUNT(*) FROM Orders WHERE Status = 'Verified' AND RequiresPrescription = 1) as ReadyToDispense;
    END
    ELSE IF @Role = 'Customer' AND @UserId IS NOT NULL
    BEGIN
        -- Customer dashboard stats
        SELECT 
            (SELECT COUNT(*) FROM Orders WHERE CustomerId = @UserId) as TotalOrders,
            (SELECT COUNT(*) FROM Orders WHERE CustomerId = @UserId AND Status = 'Processing') as ActiveOrders,
            (SELECT LoyaltyPoints FROM Users WHERE UserId = @UserId) as LoyaltyPoints,
            (SELECT COUNT(*) FROM Prescriptions WHERE UserId = @UserId) as TotalPrescriptions,
            (SELECT COUNT(*) FROM ShoppingCart WHERE UserId = @UserId) as CartItems;
    END
END;
GO

-- Create sequences for order and prescription numbers
CREATE SEQUENCE OrderNumberSequence
    START WITH 1
    INCREMENT BY 1
    MINVALUE 1
    MAXVALUE 9999
    CYCLE;

CREATE SEQUENCE PrescriptionNumberSequence
    START WITH 1
    INCREMENT BY 1
    MINVALUE 1
    MAXVALUE 9999
    CYCLE;
