using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace MediEase.Models
{
    [Table("Orders")]
    public class Order
    {
        [Key]
        public int OrderId { get; set; }

        [Required]
        [StringLength(50)]
        [Display(Name = "Order Number")]
        public string OrderNumber { get; set; }

        [Required]
        [Display(Name = "Customer")]
        public int CustomerId { get; set; }

        [ForeignKey("CustomerId")]
        public virtual User Customer { get; set; }

        [Display(Name = "Prescription")]
        public int? PrescriptionId { get; set; }

        [ForeignKey("PrescriptionId")]
        public virtual Prescription Prescription { get; set; }

        [Required]
        [Display(Name = "Order Date")]
        public DateTime OrderDate { get; set; } = DateTime.Now;

        [Required]
        [StringLength(20)]
        public string Status { get; set; } = "Pending"; // Pending, Processing, Shipped, Delivered, Cancelled

        [Required]
        [Column(TypeName = "decimal(10,2)")]
        [Display(Name = "Subtotal")]
        public decimal Subtotal { get; set; }

        [Column(TypeName = "decimal(10,2)")]
        [Display(Name = "Tax Amount")]
        public decimal TaxAmount { get; set; } = 0;

        [Column(TypeName = "decimal(10,2)")]
        [Display(Name = "Shipping Cost")]
        public decimal ShippingCost { get; set; } = 0;

        [Column(TypeName = "decimal(10,2)")]
        [Display(Name = "Discount Amount")]
        public decimal DiscountAmount { get; set; } = 0;

        [Required]
        [Column(TypeName = "decimal(10,2)")]
        [Display(Name = "Total Amount")]
        public decimal TotalAmount { get; set; }

        [StringLength(20)]
        [Display(Name = "Payment Method")]
        public string PaymentMethod { get; set; } // Cash, Card, Online, Insurance

        [StringLength(20)]
        [Display(Name = "Payment Status")]
        public string PaymentStatus { get; set; } = "Pending"; // Pending, Paid, Failed, Refunded

        [StringLength(100)]
        [Display(Name = "Payment Reference")]
        public string PaymentReference { get; set; }

        [StringLength(100)]
        [Display(Name = "Payment Transaction ID")]
        public string PaymentTransactionId { get; set; }

        [Display(Name = "Payment Date")]
        public DateTime? PaymentDate { get; set; }

        [Required]
        [StringLength(500)]
        [Display(Name = "Shipping Address")]
        public string ShippingAddress { get; set; }

        [StringLength(100)]
        [Display(Name = "Shipping City")]
        public string ShippingCity { get; set; }

        [StringLength(20)]
        [Display(Name = "Shipping Postal Code")]
        public string ShippingPostalCode { get; set; }

        [StringLength(100)]
        [Display(Name = "Shipping Country")]
        public string ShippingCountry { get; set; }

        [StringLength(15)]
        [Display(Name = "Contact Phone")]
        public string ContactPhone { get; set; }

        [StringLength(100)]
        [Display(Name = "Contact Email")]
        public string ContactEmail { get; set; }

        [StringLength(1000)]
        [Display(Name = "Special Instructions")]
        public string SpecialInstructions { get; set; }

        [Display(Name = "Expected Delivery Date")]
        public DateTime? ExpectedDeliveryDate { get; set; }

        [Display(Name = "Actual Delivery Date")]
        public DateTime? ActualDeliveryDate { get; set; }

        [StringLength(50)]
        [Display(Name = "Tracking Number")]
        public string TrackingNumber { get; set; }

        [StringLength(100)]
        [Display(Name = "Courier Service")]
        public string CourierService { get; set; }

        [Display(Name = "Is Urgent")]
        public bool IsUrgent { get; set; } = false;

        [Display(Name = "Requires Prescription")]
        public bool RequiresPrescription { get; set; } = false;

        [Display(Name = "Prescription Verified")]
        public bool PrescriptionVerified { get; set; } = false;

        [Display(Name = "Verified By")]
        public int? VerifiedBy { get; set; }

        [ForeignKey("VerifiedBy")]
        public virtual User VerifiedByUser { get; set; }

        [Display(Name = "Verification Date")]
        public DateTime? VerificationDate { get; set; }

        [StringLength(500)]
        [Display(Name = "Verification Notes")]
        public string VerificationNotes { get; set; }

        [StringLength(1000)]
        public string Notes { get; set; }

        [Display(Name = "Created Date")]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        [Display(Name = "Modified Date")]
        public DateTime? ModifiedDate { get; set; }

        [Display(Name = "Created By")]
        public int? CreatedBy { get; set; }

        [Display(Name = "Modified By")]
        public int? ModifiedBy { get; set; }

        // Navigation Properties
        public virtual ICollection<OrderItem> OrderItems { get; set; } = new List<OrderItem>();

        // Computed Properties
        [NotMapped]
        [Display(Name = "Is Completed")]
        public bool IsCompleted => Status == "Delivered";

        [NotMapped]
        [Display(Name = "Is Cancelled")]
        public bool IsCancelled => Status == "Cancelled";

        [NotMapped]
        [Display(Name = "Can Cancel")]
        public bool CanCancel => Status == "Pending" || Status == "Processing";

        [NotMapped]
        [Display(Name = "Days Since Order")]
        public int DaysSinceOrder => (DateTime.Now - OrderDate).Days;

        [NotMapped]
        [Display(Name = "Is Overdue")]
        public bool IsOverdue => ExpectedDeliveryDate.HasValue && ExpectedDeliveryDate.Value < DateTime.Today && !IsCompleted;
    }

    [Table("OrderItems")]
    public class OrderItem
    {
        [Key]
        public int OrderItemId { get; set; }

        [Required]
        [Display(Name = "Order")]
        public int OrderId { get; set; }

        [ForeignKey("OrderId")]
        public virtual Order Order { get; set; }

        [Required]
        [Display(Name = "Medicine")]
        public int MedicineId { get; set; }

        [ForeignKey("MedicineId")]
        public virtual Medicine Medicine { get; set; }

        [Required]
        [Range(1, int.MaxValue, ErrorMessage = "Quantity must be at least 1")]
        public int Quantity { get; set; }

        [Required]
        [Column(TypeName = "decimal(10,2)")]
        [Display(Name = "Unit Price")]
        public decimal UnitPrice { get; set; }

        [Column(TypeName = "decimal(10,2)")]
        [Display(Name = "Discount Amount")]
        public decimal DiscountAmount { get; set; } = 0;

        [Required]
        [Column(TypeName = "decimal(10,2)")]
        [Display(Name = "Total Price")]
        public decimal TotalPrice { get; set; }

        [StringLength(500)]
        [Display(Name = "Special Instructions")]
        public string SpecialInstructions { get; set; }

        [Display(Name = "Created Date")]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        // Computed Properties
        [NotMapped]
        [Display(Name = "Final Unit Price")]
        public decimal FinalUnitPrice => UnitPrice - (DiscountAmount / Quantity);

        [NotMapped]
        [Display(Name = "Total Discount")]
        public decimal TotalDiscount => DiscountAmount * Quantity;
    }

    public enum OrderStatus
    {
        Pending,
        Processing,
        Verified,
        Packed,
        Shipped,
        OutForDelivery,
        Delivered,
        Cancelled,
        Returned,
        Refunded
    }

    public enum PaymentMethod
    {
        Cash,
        CreditCard,
        DebitCard,
        OnlinePayment,
        Insurance,
        BankTransfer,
        DigitalWallet
    }

    public enum PaymentStatus
    {
        Pending,
        Processing,
        Paid,
        Failed,
        Cancelled,
        Refunded,
        PartiallyRefunded
    }
}
