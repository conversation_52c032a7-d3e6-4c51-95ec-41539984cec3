using System;
using System.Configuration;
using System.Threading.Tasks;
using System.Net.Http;
using System.Text;
using Newtonsoft.Json;
using MediEase.Models;
using MediEase.DAL;

namespace MediEase.Utilities
{
    public class PaymentRequest
    {
        public decimal Amount { get; set; }
        public string CardNumber { get; set; }
        public string ExpiryDate { get; set; }
        public string CVV { get; set; }
        public string CardholderName { get; set; }
        public string OrderNumber { get; set; }
        public string CustomerEmail { get; set; }
        public string ReturnUrl { get; set; }
        public string CancelUrl { get; set; }
    }
}

namespace MediEase.Utilities
{
    public static class PaymentService
    {
        private static readonly string StripeApiKey = ConfigurationManager.AppSettings["StripeSecretKey"] ?? "";
        private static readonly string StripePublishableKey = ConfigurationManager.AppSettings["StripePublishableKey"] ?? "";
        private static readonly string PayPalClientId = ConfigurationManager.AppSettings["PayPalClientId"] ?? "";
        private static readonly string PayPalClientSecret = ConfigurationManager.AppSettings["PayPalClientSecret"] ?? "";
        private static readonly bool IsTestMode = Convert.ToBoolean(ConfigurationManager.AppSettings["PaymentTestMode"] ?? "true");

        #region Stripe Integration

        public static async Task<PaymentResult> ProcessStripePaymentAsync(decimal amount, string currency, string token, string description)
        {
            try
            {
                using (var client = new HttpClient())
                {
                    client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", StripeApiKey);

                    var parameters = new StringBuilder();
                    parameters.Append($"amount={Math.Round(amount * 100)}&"); // Stripe uses cents
                    parameters.Append($"currency={currency.ToLower()}&");
                    parameters.Append($"source={token}&");
                    parameters.Append($"description={Uri.EscapeDataString(description)}");

                    var content = new StringContent(parameters.ToString(), Encoding.UTF8, "application/x-www-form-urlencoded");
                    var response = await client.PostAsync("https://api.stripe.com/v1/charges", content);
                    var responseContent = await response.Content.ReadAsStringAsync();

                    if (response.IsSuccessStatusCode)
                    {
                        var chargeResponse = JsonConvert.DeserializeObject<dynamic>(responseContent);
                        return new PaymentResult
                        {
                            IsSuccess = true,
                            TransactionId = chargeResponse.id,
                            Amount = amount,
                            Currency = currency,
                            PaymentMethod = "Stripe",
                            Message = "Payment processed successfully"
                        };
                    }
                    else
                    {
                        var errorResponse = JsonConvert.DeserializeObject<dynamic>(responseContent);
                        return new PaymentResult
                        {
                            IsSuccess = false,
                            Message = errorResponse.error?.message ?? "Payment failed"
                        };
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Stripe payment processing error");
                return new PaymentResult
                {
                    IsSuccess = false,
                    Message = "Payment processing error. Please try again."
                };
            }
        }

        #endregion

        #region PayPal Integration

        public static async Task<PaymentResult> ProcessPayPalPaymentAsync(decimal amount, string currency, string paymentId, string payerId)
        {
            try
            {
                var accessToken = await GetPayPalAccessTokenAsync();
                if (string.IsNullOrEmpty(accessToken))
                {
                    return new PaymentResult { IsSuccess = false, Message = "PayPal authentication failed" };
                }

                using (var client = new HttpClient())
                {
                    client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                    var executePayment = new
                    {
                        payer_id = payerId
                    };

                    var json = JsonConvert.SerializeObject(executePayment);
                    var content = new StringContent(json, Encoding.UTF8, "application/json");

                    var baseUrl = IsTestMode ? "https://api.sandbox.paypal.com" : "https://api.paypal.com";
                    var response = await client.PostAsync($"{baseUrl}/v1/payments/payment/{paymentId}/execute", content);
                    var responseContent = await response.Content.ReadAsStringAsync();

                    if (response.IsSuccessStatusCode)
                    {
                        var paymentResponse = JsonConvert.DeserializeObject<dynamic>(responseContent);
                        return new PaymentResult
                        {
                            IsSuccess = true,
                            TransactionId = paymentResponse.id,
                            Amount = amount,
                            Currency = currency,
                            PaymentMethod = "PayPal",
                            Message = "Payment processed successfully"
                        };
                    }
                    else
                    {
                        return new PaymentResult
                        {
                            IsSuccess = false,
                            Message = "PayPal payment execution failed"
                        };
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "PayPal payment processing error");
                return new PaymentResult
                {
                    IsSuccess = false,
                    Message = "Payment processing error. Please try again."
                };
            }
        }

        private static async Task<string> GetPayPalAccessTokenAsync()
        {
            try
            {
                using (var client = new HttpClient())
                {
                    var credentials = Convert.ToBase64String(Encoding.UTF8.GetBytes($"{PayPalClientId}:{PayPalClientSecret}"));
                    client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Basic", credentials);

                    var parameters = "grant_type=client_credentials";
                    var content = new StringContent(parameters, Encoding.UTF8, "application/x-www-form-urlencoded");

                    var baseUrl = IsTestMode ? "https://api.sandbox.paypal.com" : "https://api.paypal.com";
                    var response = await client.PostAsync($"{baseUrl}/v1/oauth2/token", content);
                    var responseContent = await response.Content.ReadAsStringAsync();

                    if (response.IsSuccessStatusCode)
                    {
                        var tokenResponse = JsonConvert.DeserializeObject<dynamic>(responseContent);
                        return tokenResponse.access_token;
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "PayPal token retrieval error");
            }

            return null;
        }

        #endregion

        #region Cash on Delivery

        public static PaymentResult ProcessCashOnDeliveryPayment(decimal amount, string currency)
        {
            return new PaymentResult
            {
                IsSuccess = true,
                TransactionId = $"COD_{DateTime.Now:yyyyMMddHHmmss}_{Guid.NewGuid().ToString("N")[..8]}",
                Amount = amount,
                Currency = currency,
                PaymentMethod = "Cash on Delivery",
                Message = "Cash on Delivery order placed successfully"
            };
        }

        #endregion

        #region Payment Recording

        public static async Task<bool> RecordPaymentAsync(int orderId, PaymentResult paymentResult)
        {
            try
            {
                using (var db = new MediEaseContext())
                {
                    var payment = new Payment
                    {
                        OrderId = orderId,
                        TransactionId = paymentResult.TransactionId,
                        Amount = paymentResult.Amount,
                        Currency = paymentResult.Currency,
                        PaymentMethod = paymentResult.PaymentMethod,
                        Status = paymentResult.IsSuccess ? "Completed" : "Failed",
                        PaymentDate = DateTime.Now,
                        GatewayResponse = paymentResult.Message
                    };

                    db.Payments.Add(payment);
                    await db.SaveChangesAsync();

                    // Update order payment status
                    var order = await db.Orders.FindAsync(orderId);
                    if (order != null)
                    {
                        order.PaymentStatus = paymentResult.IsSuccess ? "Paid" : "Failed";
                        order.PaymentMethod = paymentResult.PaymentMethod;
                        if (paymentResult.IsSuccess)
                        {
                            order.Status = "Processing";
                        }
                        await db.SaveChangesAsync();
                    }

                    return true;
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error recording payment");
                return false;
            }
        }

        #endregion

        #region Refund Processing

        public static async Task<PaymentResult> ProcessRefundAsync(string transactionId, decimal amount, string reason)
        {
            try
            {
                using (var db = new MediEaseContext())
                {
                    var payment = db.Payments.FirstOrDefault(p => p.TransactionId == transactionId);
                    if (payment == null)
                    {
                        return new PaymentResult { IsSuccess = false, Message = "Payment not found" };
                    }

                    PaymentResult refundResult = null;

                    switch (payment.PaymentMethod.ToLower())
                    {
                        case "stripe":
                            refundResult = await ProcessStripeRefundAsync(transactionId, amount);
                            break;
                        case "paypal":
                            refundResult = await ProcessPayPalRefundAsync(transactionId, amount);
                            break;
                        case "cash on delivery":
                            refundResult = new PaymentResult
                            {
                                IsSuccess = true,
                                TransactionId = $"REF_{DateTime.Now:yyyyMMddHHmmss}",
                                Amount = amount,
                                PaymentMethod = "Cash Refund",
                                Message = "Cash refund processed"
                            };
                            break;
                        default:
                            return new PaymentResult { IsSuccess = false, Message = "Unsupported payment method for refund" };
                    }

                    if (refundResult.IsSuccess)
                    {
                        // Record refund in database
                        var refund = new Refund
                        {
                            PaymentId = payment.PaymentId,
                            RefundTransactionId = refundResult.TransactionId,
                            Amount = amount,
                            Reason = reason,
                            Status = "Completed",
                            RefundDate = DateTime.Now
                        };

                        db.Refunds.Add(refund);
                        await db.SaveChangesAsync();
                    }

                    return refundResult;
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error processing refund");
                return new PaymentResult { IsSuccess = false, Message = "Refund processing error" };
            }
        }

        private static async Task<PaymentResult> ProcessStripeRefundAsync(string chargeId, decimal amount)
        {
            try
            {
                using (var client = new HttpClient())
                {
                    client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", StripeApiKey);

                    var parameters = $"charge={chargeId}&amount={Math.Round(amount * 100)}";
                    var content = new StringContent(parameters, Encoding.UTF8, "application/x-www-form-urlencoded");

                    var response = await client.PostAsync("https://api.stripe.com/v1/refunds", content);
                    var responseContent = await response.Content.ReadAsStringAsync();

                    if (response.IsSuccessStatusCode)
                    {
                        var refundResponse = JsonConvert.DeserializeObject<dynamic>(responseContent);
                        return new PaymentResult
                        {
                            IsSuccess = true,
                            TransactionId = refundResponse.id,
                            Amount = amount,
                            PaymentMethod = "Stripe Refund",
                            Message = "Refund processed successfully"
                        };
                    }
                    else
                    {
                        return new PaymentResult { IsSuccess = false, Message = "Stripe refund failed" };
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Stripe refund error");
                return new PaymentResult { IsSuccess = false, Message = "Refund processing error" };
            }
        }

        private static async Task<PaymentResult> ProcessPayPalRefundAsync(string paymentId, decimal amount)
        {
            // PayPal refund implementation would go here
            // This is a simplified version
            return new PaymentResult
            {
                IsSuccess = true,
                TransactionId = $"PP_REF_{DateTime.Now:yyyyMMddHHmmss}",
                Amount = amount,
                PaymentMethod = "PayPal Refund",
                Message = "PayPal refund processed"
            };
        }

        #endregion

        #region Utility Methods

        public static string GetStripePublishableKey()
        {
            return StripePublishableKey;
        }

        public static bool IsPaymentConfigured()
        {
            return !string.IsNullOrEmpty(StripeApiKey) || !string.IsNullOrEmpty(PayPalClientId);
        }

        public static decimal CalculateProcessingFee(decimal amount, string paymentMethod)
        {
            switch (paymentMethod.ToLower())
            {
                case "stripe":
                    return Math.Round(amount * 0.029m + 0.30m, 2); // 2.9% + $0.30
                case "paypal":
                    return Math.Round(amount * 0.0349m + 0.49m, 2); // 3.49% + $0.49
                default:
                    return 0;
            }
        }

        // Methods for checkout compatibility
        public static PaymentResult ProcessCreditCardPayment(PaymentRequest request)
        {
            try
            {
                // Validate request
                if (request == null || request.Amount <= 0)
                {
                    return new PaymentResult
                    {
                        IsSuccess = false,
                        Message = "Invalid payment request"
                    };
                }

                // For demo purposes, simulate credit card processing
                // In production, use ProcessStripePaymentAsync or similar

                // Basic validation
                var cardNumber = request.CardNumber?.Replace(" ", "").Replace("-", "");
                if (string.IsNullOrEmpty(cardNumber) || cardNumber.Length < 13)
                {
                    return new PaymentResult
                    {
                        IsSuccess = false,
                        Message = "Invalid card number"
                    };
                }

                // Simulate processing
                System.Threading.Thread.Sleep(2000);

                // Simulate success/failure based on card number
                var lastDigit = int.Parse(cardNumber.Substring(cardNumber.Length - 1));

                if (lastDigit == 1)
                {
                    return new PaymentResult
                    {
                        IsSuccess = false,
                        Message = "Card declined by issuer"
                    };
                }
                else if (lastDigit == 2)
                {
                    return new PaymentResult
                    {
                        IsSuccess = false,
                        Message = "Insufficient funds"
                    };
                }
                else
                {
                    var transactionId = GenerateTransactionId();

                    ErrorLogger.LogInfo($"Credit card payment processed successfully. Transaction ID: {transactionId}, Amount: ${request.Amount:F2}", "PaymentService");

                    return new PaymentResult
                    {
                        IsSuccess = true,
                        TransactionId = transactionId,
                        PaymentMethod = "Credit Card",
                        ProcessedDate = DateTime.Now,
                        Amount = request.Amount,
                        Message = "Payment processed successfully"
                    };
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "PaymentService.ProcessCreditCardPayment");
                return new PaymentResult
                {
                    IsSuccess = false,
                    Message = "Payment processing error occurred"
                };
            }
        }

        public static PaymentResult ProcessPayPalPayment(PaymentRequest request)
        {
            try
            {
                // Validate request
                if (request == null || request.Amount <= 0)
                {
                    return new PaymentResult
                    {
                        IsSuccess = false,
                        Message = "Invalid payment request"
                    };
                }

                // Simulate PayPal processing
                System.Threading.Thread.Sleep(1500);

                // Simulate 98% success rate
                var isSuccess = new Random().Next(1, 101) <= 98;

                if (isSuccess)
                {
                    var transactionId = GenerateTransactionId();

                    ErrorLogger.LogInfo($"PayPal payment processed successfully. Transaction ID: {transactionId}, Amount: ${request.Amount:F2}", "PaymentService");

                    return new PaymentResult
                    {
                        IsSuccess = true,
                        TransactionId = transactionId,
                        PaymentMethod = "PayPal",
                        ProcessedDate = DateTime.Now,
                        Amount = request.Amount,
                        Message = "Payment processed successfully"
                    };
                }
                else
                {
                    return new PaymentResult
                    {
                        IsSuccess = false,
                        Message = "PayPal payment was cancelled or failed"
                    };
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "PaymentService.ProcessPayPalPayment");
                return new PaymentResult
                {
                    IsSuccess = false,
                    Message = "PayPal processing error occurred"
                };
            }
        }

        public static string GenerateTransactionId()
        {
            return "TXN" + DateTime.Now.ToString("yyyyMMddHHmmss") + new Random().Next(1000, 9999);
        }

        #endregion
    }

    public class PaymentResult
    {
        public bool IsSuccess { get; set; }
        public string TransactionId { get; set; }
        public decimal Amount { get; set; }
        public string Currency { get; set; } = "USD";
        public string PaymentMethod { get; set; }
        public string Message { get; set; }
        public DateTime ProcessedDate { get; set; } = DateTime.Now;
    }
}
