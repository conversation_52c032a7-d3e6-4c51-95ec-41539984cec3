using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace MediEase.Models
{
    [Table("Categories")]
    public class Category
    {
        [Key]
        public int CategoryId { get; set; }

        [Required]
        [StringLength(100)]
        public string Name { get; set; }

        [StringLength(500)]
        public string Description { get; set; }

        [StringLength(255)]
        public string ImagePath { get; set; }

        [Display(Name = "Is Active")]
        public bool IsActive { get; set; } = true;

        [Display(Name = "Sort Order")]
        public int SortOrder { get; set; } = 0;

        [Display(Name = "Created Date")]
        public DateTime CreatedDate { get; set; } = DateTime.Now;
    }

    [Table("Brands")]
    public class Brand
    {
        [Key]
        public int BrandId { get; set; }

        [Required]
        [StringLength(100)]
        public string Name { get; set; }

        [StringLength(500)]
        public string Description { get; set; }

        [StringLength(255)]
        public string LogoPath { get; set; }

        [StringLength(200)]
        public string Website { get; set; }

        [Display(Name = "Is Active")]
        public bool IsActive { get; set; } = true;

        [Display(Name = "Created Date")]
        public DateTime CreatedDate { get; set; } = DateTime.Now;
    }

    [Table("Reviews")]
    public class Review
    {
        [Key]
        public int ReviewId { get; set; }

        [Required]
        public int MedicineId { get; set; }

        [ForeignKey("MedicineId")]
        public virtual Medicine Medicine { get; set; }

        [Required]
        public int UserId { get; set; }

        [ForeignKey("UserId")]
        public virtual User User { get; set; }

        [Required]
        [Range(1, 5)]
        public int Rating { get; set; }

        [StringLength(1000)]
        public string Comment { get; set; }

        [Display(Name = "Is Verified Purchase")]
        public bool IsVerifiedPurchase { get; set; } = false;

        [Display(Name = "Is Approved")]
        public bool IsApproved { get; set; } = false;

        [Display(Name = "Created Date")]
        public DateTime CreatedDate { get; set; } = DateTime.Now;
    }

    [Table("CartItems")]
    public class CartItem
    {
        [Key]
        public int CartItemId { get; set; }

        [Required]
        public int UserId { get; set; }

        [ForeignKey("UserId")]
        public virtual User User { get; set; }

        [Required]
        public int MedicineId { get; set; }

        [ForeignKey("MedicineId")]
        public virtual Medicine Medicine { get; set; }

        [Required]
        [Range(1, int.MaxValue)]
        public int Quantity { get; set; }

        [Required]
        [Column(TypeName = "decimal(10,2)")]
        [Display(Name = "Unit Price")]
        public decimal UnitPrice { get; set; }

        [Required]
        [Column(TypeName = "decimal(10,2)")]
        [Display(Name = "Total Price")]
        public decimal TotalPrice { get; set; }

        [Display(Name = "Created Date")]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        [Display(Name = "Modified Date")]
        public DateTime? ModifiedDate { get; set; }
    }

    [Table("Notifications")]
    public class Notification
    {
        [Key]
        public int NotificationId { get; set; }

        [Required]
        public int UserId { get; set; }

        [ForeignKey("UserId")]
        public virtual User User { get; set; }

        [Required]
        [StringLength(200)]
        public string Title { get; set; }

        [Required]
        [StringLength(1000)]
        public string Message { get; set; }

        [StringLength(50)]
        public string Type { get; set; } // Info, Warning, Error, Success

        [Display(Name = "Is Read")]
        public bool IsRead { get; set; } = false;

        [StringLength(255)]
        public string ActionUrl { get; set; }

        [Display(Name = "Created Date")]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        [Display(Name = "Read Date")]
        public DateTime? ReadDate { get; set; }
    }

    [Table("AuditLogs")]
    public class AuditLog
    {
        [Key]
        public int AuditLogId { get; set; }

        [Required]
        [StringLength(50)]
        public string TableName { get; set; }

        [Required]
        [StringLength(50)]
        public string Action { get; set; } // Insert, Update, Delete

        [Required]
        public string RecordId { get; set; }

        public string OldValues { get; set; }

        public string NewValues { get; set; }

        [Required]
        public int UserId { get; set; }

        [ForeignKey("UserId")]
        public virtual User User { get; set; }

        [Display(Name = "Created Date")]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        [StringLength(45)]
        public string IPAddress { get; set; }

        [StringLength(500)]
        public string UserAgent { get; set; }
    }

    [Table("ChatMessages")]
    public class ChatMessage
    {
        [Key]
        public int ChatMessageId { get; set; }

        public int? UserId { get; set; }

        [ForeignKey("UserId")]
        public virtual User User { get; set; }

        [Required]
        [StringLength(50)]
        public string SessionId { get; set; }

        [Required]
        [StringLength(2000)]
        public string Message { get; set; }

        [Required]
        [StringLength(20)]
        public string Sender { get; set; } // User, Bot

        [StringLength(50)]
        public string MessageType { get; set; } // Text, Image, File

        public string Response { get; set; }

        [Display(Name = "Response Time")]
        public TimeSpan? ResponseTime { get; set; }

        [Display(Name = "Is Helpful")]
        public bool? IsHelpful { get; set; }

        [Display(Name = "Created Date")]
        public DateTime CreatedDate { get; set; } = DateTime.Now;
    }

    [Table("Discounts")]
    public class Discount
    {
        [Key]
        public int DiscountId { get; set; }

        [Required]
        [StringLength(100)]
        public string Name { get; set; }

        [StringLength(500)]
        public string Description { get; set; }

        [Required]
        [StringLength(20)]
        public string Type { get; set; } // Percentage, FixedAmount, BuyOneGetOne

        [Column(TypeName = "decimal(10,2)")]
        public decimal Value { get; set; }

        [Column(TypeName = "decimal(10,2)")]
        [Display(Name = "Minimum Order Amount")]
        public decimal MinimumOrderAmount { get; set; } = 0;

        [Column(TypeName = "decimal(10,2)")]
        [Display(Name = "Maximum Discount Amount")]
        public decimal MaximumDiscountAmount { get; set; } = 0;

        [Display(Name = "Start Date")]
        public DateTime StartDate { get; set; }

        [Display(Name = "End Date")]
        public DateTime EndDate { get; set; }

        [Display(Name = "Usage Limit")]
        public int? UsageLimit { get; set; }

        [Display(Name = "Usage Count")]
        public int UsageCount { get; set; } = 0;

        [Display(Name = "Is Active")]
        public bool IsActive { get; set; } = true;

        [StringLength(50)]
        [Display(Name = "Coupon Code")]
        public string CouponCode { get; set; }

        [Display(Name = "Created Date")]
        public DateTime CreatedDate { get; set; } = DateTime.Now;
    }

    [Table("LoyaltyTransactions")]
    public class LoyaltyTransaction
    {
        [Key]
        public int LoyaltyTransactionId { get; set; }

        [Required]
        public int UserId { get; set; }

        [ForeignKey("UserId")]
        public virtual User User { get; set; }

        [Required]
        [StringLength(20)]
        public string Type { get; set; } // Earned, Redeemed, Expired

        [Required]
        public int Points { get; set; }

        [StringLength(200)]
        public string Description { get; set; }

        public int? OrderId { get; set; }

        [ForeignKey("OrderId")]
        public virtual Order Order { get; set; }

        [Display(Name = "Created Date")]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        [Display(Name = "Expiry Date")]
        public DateTime? ExpiryDate { get; set; }
    }

    [Table("ErrorLogs")]
    public class ErrorLog
    {
        [Key]
        public int ErrorLogId { get; set; }

        [Required]
        [StringLength(1000)]
        public string ErrorMessage { get; set; }

        public string StackTrace { get; set; }

        [StringLength(500)]
        public string Source { get; set; }

        [StringLength(500)]
        public string TargetSite { get; set; }

        [StringLength(1000)]
        public string InnerException { get; set; }

        [StringLength(2000)]
        public string RequestUrl { get; set; }

        [StringLength(10)]
        public string RequestMethod { get; set; }

        [StringLength(1000)]
        public string UserAgent { get; set; }

        [StringLength(45)]
        public string IPAddress { get; set; }

        public int? UserId { get; set; }

        [ForeignKey("UserId")]
        public virtual User User { get; set; }

        [Display(Name = "Created Date")]
        public DateTime CreatedDate { get; set; } = DateTime.Now;
    }

    [Table("ContactMessages")]
    public class ContactMessage
    {
        [Key]
        public int ContactMessageId { get; set; }

        [Required]
        [StringLength(100)]
        public string Name { get; set; }

        [Required]
        [StringLength(100)]
        [EmailAddress]
        public string Email { get; set; }

        [StringLength(15)]
        public string Phone { get; set; }

        [Required]
        [StringLength(100)]
        public string Subject { get; set; }

        [Required]
        [StringLength(2000)]
        public string Message { get; set; }

        public bool SubscribeToNewsletter { get; set; } = false;

        [StringLength(20)]
        public string Status { get; set; } = "New"; // New, InProgress, Resolved, Closed

        [StringLength(1000)]
        public string AdminResponse { get; set; }

        public int? RespondedBy { get; set; }

        [ForeignKey("RespondedBy")]
        public virtual User RespondedByUser { get; set; }

        public DateTime? ResponseDate { get; set; }

        [StringLength(45)]
        public string IPAddress { get; set; }

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        public DateTime? ModifiedDate { get; set; }
    }

    [Table("PasswordResets")]
    public class PasswordReset
    {
        [Key]
        public int PasswordResetId { get; set; }

        [Required]
        public int UserId { get; set; }

        [ForeignKey("UserId")]
        public virtual User User { get; set; }

        [Required]
        [StringLength(100)]
        public string ResetToken { get; set; }

        [Required]
        public DateTime ExpiryDate { get; set; }

        public bool IsUsed { get; set; } = false;

        public DateTime? UsedDate { get; set; }

        [StringLength(45)]
        public string IPAddress { get; set; }

        [StringLength(45)]
        public string UsedIPAddress { get; set; }

        public DateTime CreatedDate { get; set; } = DateTime.Now;
    }

    [Table("FamilyMembers")]
    public class FamilyMember
    {
        [Key]
        public int FamilyMemberId { get; set; }

        [Required]
        [Display(Name = "Primary User")]
        public int PrimaryUserId { get; set; }

        [ForeignKey("PrimaryUserId")]
        public virtual User PrimaryUser { get; set; }

        [Required]
        [StringLength(100)]
        [Display(Name = "First Name")]
        public string FirstName { get; set; }

        [Required]
        [StringLength(100)]
        [Display(Name = "Last Name")]
        public string LastName { get; set; }

        [Required]
        [Display(Name = "Date of Birth")]
        [DataType(DataType.Date)]
        public DateTime DateOfBirth { get; set; }

        [StringLength(10)]
        public string Gender { get; set; }

        [Required]
        [StringLength(50)]
        public string Relationship { get; set; }

        [StringLength(10)]
        [Display(Name = "Blood Type")]
        public string BloodType { get; set; }

        [StringLength(1000)]
        public string Allergies { get; set; }

        [StringLength(1000)]
        [Display(Name = "Current Medications")]
        public string CurrentMedications { get; set; }

        [StringLength(1000)]
        [Display(Name = "Medical Conditions")]
        public string MedicalConditions { get; set; }

        [StringLength(100)]
        [Display(Name = "Emergency Contact Name")]
        public string EmergencyContactName { get; set; }

        [StringLength(15)]
        [Display(Name = "Emergency Contact Phone")]
        public string EmergencyContactPhone { get; set; }

        [StringLength(255)]
        [Display(Name = "Profile Image")]
        public string ProfileImage { get; set; }

        [StringLength(1000)]
        public string Notes { get; set; }

        [Display(Name = "Is Active")]
        public bool IsActive { get; set; } = true;

        [Display(Name = "Created Date")]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        [Display(Name = "Modified Date")]
        public DateTime? ModifiedDate { get; set; }

        // Navigation Properties
        public virtual ICollection<Prescription> Prescriptions { get; set; } = new List<Prescription>();
        public virtual ICollection<Order> Orders { get; set; } = new List<Order>();

        // Computed Properties
        [NotMapped]
        [Display(Name = "Full Name")]
        public string FullName => $"{FirstName} {LastName}";

        [NotMapped]
        [Display(Name = "Age")]
        public int Age
        {
            get
            {
                var today = DateTime.Today;
                var age = today.Year - DateOfBirth.Year;
                if (DateOfBirth.Date > today.AddYears(-age)) age--;
                return age;
            }
        }

        [NotMapped]
        [Display(Name = "Has Allergies")]
        public bool HasAllergies => !string.IsNullOrEmpty(Allergies);

        [NotMapped]
        [Display(Name = "Has Current Medications")]
        public bool HasCurrentMedications => !string.IsNullOrEmpty(CurrentMedications);
    }

    [Table("LoyaltyPoints")]
    public class LoyaltyPoint
    {
        [Key]
        public int LoyaltyPointId { get; set; }

        [Required]
        [Display(Name = "User")]
        public int UserId { get; set; }

        [ForeignKey("UserId")]
        public virtual User User { get; set; }

        [Required]
        [Display(Name = "Transaction Type")]
        [StringLength(50)]
        public string TransactionType { get; set; } // Earned, Redeemed, Expired, Bonus

        [Required]
        [Display(Name = "Points")]
        public int Points { get; set; }

        [Display(Name = "Order")]
        public int? OrderId { get; set; }

        [ForeignKey("OrderId")]
        public virtual Order Order { get; set; }

        [StringLength(500)]
        [Display(Name = "Description")]
        public string Description { get; set; }

        [Display(Name = "Transaction Date")]
        public DateTime TransactionDate { get; set; } = DateTime.Now;

        [Display(Name = "Expiry Date")]
        public DateTime? ExpiryDate { get; set; }

        [Display(Name = "Is Active")]
        public bool IsActive { get; set; } = true;

        // Computed Properties
        [NotMapped]
        [Display(Name = "Is Expired")]
        public bool IsExpired => ExpiryDate.HasValue && ExpiryDate.Value < DateTime.Today;
    }

    [Table("PriceComparisons")]
    public class PriceComparison
    {
        [Key]
        public int PriceComparisonId { get; set; }

        [Required]
        [Display(Name = "Medicine")]
        public int MedicineId { get; set; }

        [ForeignKey("MedicineId")]
        public virtual Medicine Medicine { get; set; }

        [Required]
        [StringLength(200)]
        [Display(Name = "Pharmacy Name")]
        public string PharmacyName { get; set; }

        [Required]
        [Column(TypeName = "decimal(10,2)")]
        public decimal Price { get; set; }

        [Column(TypeName = "decimal(10,2)")]
        [Display(Name = "Discount Price")]
        public decimal? DiscountPrice { get; set; }

        [Display(Name = "In Stock")]
        public bool InStock { get; set; } = true;

        [StringLength(500)]
        [Display(Name = "Pharmacy Address")]
        public string PharmacyAddress { get; set; }

        [StringLength(15)]
        [Display(Name = "Pharmacy Phone")]
        public string PharmacyPhone { get; set; }

        [StringLength(100)]
        [Display(Name = "Pharmacy Website")]
        public string PharmacyWebsite { get; set; }

        [Display(Name = "Last Updated")]
        public DateTime LastUpdated { get; set; } = DateTime.Now;

        [Display(Name = "Is Active")]
        public bool IsActive { get; set; } = true;

        // Computed Properties
        [NotMapped]
        [Display(Name = "Final Price")]
        public decimal FinalPrice => DiscountPrice ?? Price;

        [NotMapped]
        [Display(Name = "Has Discount")]
        public bool HasDiscount => DiscountPrice.HasValue && DiscountPrice < Price;

        [NotMapped]
        [Display(Name = "Discount Percentage")]
        public decimal? DiscountPercentage
        {
            get
            {
                if (!HasDiscount) return null;
                return Math.Round(((Price - DiscountPrice.Value) / Price) * 100, 2);
            }
        }
    }

    // Auto-Refill Model
    [Table("AutoRefills")]
    public class AutoRefill
    {
        [Key]
        public int AutoRefillId { get; set; }

        [Required]
        [Display(Name = "User ID")]
        public int UserId { get; set; }

        [Required]
        [Display(Name = "Medicine ID")]
        public int MedicineId { get; set; }

        [Required]
        [Range(1, 1000)]
        [Display(Name = "Quantity")]
        public int Quantity { get; set; }

        [Required]
        [Range(1, 365)]
        [Display(Name = "Refill Frequency (Days)")]
        public int RefillFrequencyDays { get; set; }

        [Required]
        [Display(Name = "Next Refill Date")]
        public DateTime NextRefillDate { get; set; }

        [Required]
        [Column(TypeName = "decimal(10,2)")]
        [Display(Name = "Unit Price")]
        public decimal UnitPrice { get; set; }

        [Display(Name = "Is Active")]
        public bool IsActive { get; set; } = true;

        [StringLength(500)]
        [Display(Name = "Notes")]
        public string Notes { get; set; }

        [Display(Name = "Email Reminders")]
        public bool EmailReminders { get; set; } = true;

        [Display(Name = "SMS Reminders")]
        public bool SMSReminders { get; set; } = false;

        [Display(Name = "Created Date")]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        [Display(Name = "Last Refill Date")]
        public DateTime? LastRefillDate { get; set; }

        // Navigation Properties
        [ForeignKey("UserId")]
        public virtual User User { get; set; }

        [ForeignKey("MedicineId")]
        public virtual Medicine Medicine { get; set; }

        // Computed Properties
        [NotMapped]
        [Display(Name = "Status")]
        public string Status => IsActive ? "Active" : "Paused";

        [NotMapped]
        [Display(Name = "Days Until Next Refill")]
        public int DaysUntilNextRefill => (NextRefillDate - DateTime.Today).Days;
    }

    // Health Reminder Model
    [Table("HealthReminders")]
    public class HealthReminder
    {
        [Key]
        public int ReminderId { get; set; }

        [Required]
        [Display(Name = "User ID")]
        public int UserId { get; set; }

        [Required]
        [StringLength(20)]
        [Display(Name = "Reminder Type")]
        public string Type { get; set; } // Medication, Appointment, Health, Refill

        [Required]
        [StringLength(200)]
        [Display(Name = "Title")]
        public string Title { get; set; }

        [StringLength(500)]
        [Display(Name = "Description")]
        public string Description { get; set; }

        [Required]
        [Display(Name = "Reminder Time")]
        public TimeSpan ReminderTime { get; set; }

        [Required]
        [StringLength(20)]
        [Display(Name = "Frequency")]
        public string Frequency { get; set; } // Daily, Weekly, Monthly, AsNeeded, Custom

        [Required]
        [Display(Name = "Start Date")]
        public DateTime StartDate { get; set; }

        [Display(Name = "End Date")]
        public DateTime? EndDate { get; set; }

        [Display(Name = "Medicine ID")]
        public int? MedicineId { get; set; }

        [StringLength(100)]
        [Display(Name = "Medicine Name")]
        public string MedicineName { get; set; }

        [StringLength(100)]
        [Display(Name = "Dosage")]
        public string Dosage { get; set; }

        [StringLength(1000)]
        [Display(Name = "Notes")]
        public string Notes { get; set; }

        [Display(Name = "Is Active")]
        public bool IsActive { get; set; } = true;

        [Display(Name = "Email Notifications")]
        public bool EmailNotifications { get; set; } = true;

        [Display(Name = "SMS Notifications")]
        public bool SMSNotifications { get; set; } = false;

        [Display(Name = "Push Notifications")]
        public bool PushNotifications { get; set; } = true;

        [Display(Name = "Created Date")]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        [Display(Name = "Last Triggered")]
        public DateTime? LastTriggered { get; set; }

        // Navigation Properties
        [ForeignKey("UserId")]
        public virtual User User { get; set; }

        [ForeignKey("MedicineId")]
        public virtual Medicine Medicine { get; set; }
    }

    // Feedback Model
    [Table("Feedback")]
    public class Feedback
    {
        [Key]
        public int FeedbackId { get; set; }

        [Required]
        [Display(Name = "User ID")]
        public int UserId { get; set; }

        [Required]
        [StringLength(50)]
        [Display(Name = "Feedback Type")]
        public string FeedbackType { get; set; } // General, Complaint, Suggestion, Bug, Feature, Support

        [Required]
        [StringLength(20)]
        [Display(Name = "Priority")]
        public string Priority { get; set; } = "Medium"; // Low, Medium, High, Urgent

        [Required]
        [StringLength(200)]
        [Display(Name = "Subject")]
        public string Subject { get; set; }

        [Required]
        [StringLength(2000)]
        [Display(Name = "Message")]
        public string Message { get; set; }

        [Display(Name = "Related Order ID")]
        public int? RelatedOrderId { get; set; }

        [StringLength(20)]
        [Display(Name = "Status")]
        public string Status { get; set; } = "Pending"; // Pending, InProgress, Resolved, Closed

        [StringLength(2000)]
        [Display(Name = "Response")]
        public string Response { get; set; }

        [Display(Name = "Responded By")]
        public int? RespondedBy { get; set; }

        [Display(Name = "Response Date")]
        public DateTime? ResponseDate { get; set; }

        [Display(Name = "Email Updates")]
        public bool EmailUpdates { get; set; } = true;

        [Display(Name = "Created Date")]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        [Display(Name = "Modified Date")]
        public DateTime? ModifiedDate { get; set; }

        // Navigation Properties
        [ForeignKey("UserId")]
        public virtual User User { get; set; }

        [ForeignKey("RelatedOrderId")]
        public virtual Order RelatedOrder { get; set; }

        [ForeignKey("RespondedBy")]
        public virtual User Responder { get; set; }
    }

    // Accessibility Settings Model
    [Table("AccessibilitySettings")]
    public class AccessibilitySettings
    {
        [Key]
        public int SettingsId { get; set; }

        [Required]
        [Display(Name = "User ID")]
        public int UserId { get; set; }

        [Range(12, 24)]
        [Display(Name = "Font Size")]
        public int FontSize { get; set; } = 16;

        [StringLength(20)]
        [Display(Name = "Theme")]
        public string Theme { get; set; } = "light"; // light, dark, high-contrast

        [StringLength(20)]
        [Display(Name = "Color Scheme")]
        public string ColorScheme { get; set; } = "default";

        [StringLength(50)]
        [Display(Name = "Font Family")]
        public string FontFamily { get; set; } = "default";

        [Display(Name = "Text-to-Speech Enabled")]
        public bool TextToSpeechEnabled { get; set; } = false;

        [Range(0.5, 2.0)]
        [Display(Name = "Speech Rate")]
        public double SpeechRate { get; set; } = 1.0;

        [Display(Name = "Sound Effects Enabled")]
        public bool SoundEffectsEnabled { get; set; } = true;

        [Display(Name = "Enhanced Keyboard Navigation")]
        public bool EnhancedKeyboardNav { get; set; } = false;

        [Display(Name = "Skip Links")]
        public bool SkipLinks { get; set; } = false;

        [Display(Name = "Show Keyboard Shortcuts")]
        public bool ShowKeyboardShortcuts { get; set; } = false;

        [Display(Name = "Reduce Motion")]
        public bool ReduceMotion { get; set; } = false;

        [Display(Name = "Enhanced Focus")]
        public bool EnhancedFocus { get; set; } = false;

        [Display(Name = "Underline Links")]
        public bool UnderlineLinks { get; set; } = false;

        [Display(Name = "Created Date")]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        [Display(Name = "Modified Date")]
        public DateTime? ModifiedDate { get; set; }

        // Navigation Properties
        [ForeignKey("UserId")]
        public virtual User User { get; set; }
    }



    // Internal Message Model
    [Table("InternalMessages")]
    public class InternalMessage
    {
        [Key]
        public int MessageId { get; set; }

        [Required]
        [Display(Name = "Sender ID")]
        public int SenderId { get; set; }

        [Required]
        [Display(Name = "Recipient ID")]
        public int RecipientId { get; set; }

        [Required]
        [StringLength(200)]
        [Display(Name = "Subject")]
        public string Subject { get; set; }

        [Required]
        [StringLength(5000)]
        [Display(Name = "Message")]
        public string Message { get; set; }

        [StringLength(20)]
        [Display(Name = "Priority")]
        public string Priority { get; set; } = "Medium"; // Low, Medium, High

        [Display(Name = "Is Read")]
        public bool IsRead { get; set; } = false;

        [Display(Name = "Is Important")]
        public bool IsImportant { get; set; } = false;

        [Display(Name = "Is Archived")]
        public bool IsArchived { get; set; } = false;

        [Display(Name = "Is Draft")]
        public bool IsDraft { get; set; } = false;

        [Display(Name = "Has Attachment")]
        public bool HasAttachment { get; set; } = false;

        [StringLength(1000)]
        [Display(Name = "Attachment Path")]
        public string AttachmentPath { get; set; }

        [Display(Name = "Read Receipt Requested")]
        public bool ReadReceiptRequested { get; set; } = false;

        [Display(Name = "Read Date")]
        public DateTime? ReadDate { get; set; }

        [Display(Name = "Created Date")]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        [Display(Name = "Reply To Message ID")]
        public int? ReplyToMessageId { get; set; }

        // Navigation Properties
        [ForeignKey("SenderId")]
        public virtual User Sender { get; set; }

        [ForeignKey("RecipientId")]
        public virtual User Recipient { get; set; }

        [ForeignKey("ReplyToMessageId")]
        public virtual InternalMessage ReplyToMessage { get; set; }

        // Computed Properties
        [NotMapped]
        [Display(Name = "Sender Name")]
        public string SenderName => Sender != null ? $"{Sender.FirstName} {Sender.LastName}" : "";

        [NotMapped]
        [Display(Name = "Recipient Name")]
        public string RecipientName => Recipient != null ? $"{Recipient.FirstName} {Recipient.LastName}" : "";
    }

    // Bulk Upload History Model
    [Table("BulkUploadHistory")]
    public class BulkUploadHistory
    {
        [Key]
        public int UploadId { get; set; }

        [Required]
        [Display(Name = "User ID")]
        public int UserId { get; set; }

        [Required]
        [StringLength(50)]
        [Display(Name = "Upload Type")]
        public string UploadType { get; set; } // Medicines, Categories, Brands, Offers, Stock

        [Required]
        [StringLength(255)]
        [Display(Name = "File Name")]
        public string FileName { get; set; }

        [Required]
        [StringLength(500)]
        [Display(Name = "File Path")]
        public string FilePath { get; set; }

        [Display(Name = "Total Records")]
        public int TotalRecords { get; set; }

        [Display(Name = "Successful Records")]
        public int SuccessfulRecords { get; set; }

        [Display(Name = "Failed Records")]
        public int FailedRecords { get; set; }

        [Display(Name = "Warning Records")]
        public int WarningRecords { get; set; }

        [StringLength(20)]
        [Display(Name = "Status")]
        public string Status { get; set; } = "Processing"; // Processing, Completed, Failed

        [StringLength(5000)]
        [Display(Name = "Error Log")]
        public string ErrorLog { get; set; }

        [Display(Name = "Started Date")]
        public DateTime StartedDate { get; set; } = DateTime.Now;

        [Display(Name = "Completed Date")]
        public DateTime? CompletedDate { get; set; }

        [Display(Name = "Processing Time (seconds)")]
        public int? ProcessingTimeSeconds { get; set; }

        // Navigation Properties
        [ForeignKey("UserId")]
        public virtual User User { get; set; }

        // Computed Properties
        [NotMapped]
        [Display(Name = "Success Rate")]
        public decimal SuccessRate => TotalRecords > 0 ? (decimal)SuccessfulRecords / TotalRecords * 100 : 0;

        [NotMapped]
        [Display(Name = "Duration")]
        public string Duration
        {
            get
            {
                if (ProcessingTimeSeconds.HasValue)
                {
                    var ts = TimeSpan.FromSeconds(ProcessingTimeSeconds.Value);
                    return $"{ts.Minutes:D2}:{ts.Seconds:D2}";
                }
                return "N/A";
            }
        }
    }

    // Report Schedule Model
    [Table("ReportSchedules")]
    public class ReportSchedule
    {
        [Key]
        public int ScheduleId { get; set; }

        [Required]
        [Display(Name = "User ID")]
        public int UserId { get; set; }

        [Required]
        [StringLength(50)]
        [Display(Name = "Report Type")]
        public string ReportType { get; set; } // Sales, Inventory, Orders, Customers

        [Required]
        [StringLength(20)]
        [Display(Name = "Frequency")]
        public string Frequency { get; set; } // Daily, Weekly, Monthly

        [Required]
        [StringLength(1000)]
        [Display(Name = "Recipients")]
        public string Recipients { get; set; } // Comma-separated email addresses

        [Display(Name = "Include Charts")]
        public bool IncludeCharts { get; set; } = true;

        [Display(Name = "Is Active")]
        public bool IsActive { get; set; } = true;

        [Display(Name = "Next Run Date")]
        public DateTime NextRunDate { get; set; }

        [Display(Name = "Last Run Date")]
        public DateTime? LastRunDate { get; set; }

        [Display(Name = "Created Date")]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        [StringLength(1000)]
        [Display(Name = "Parameters")]
        public string Parameters { get; set; } // JSON string for additional parameters

        // Navigation Properties
        [ForeignKey("UserId")]
        public virtual User User { get; set; }
    }

    // System Configuration Model
    [Table("SystemConfigurations")]
    public class SystemConfiguration
    {
        [Key]
        public int ConfigId { get; set; }

        [Required]
        [StringLength(100)]
        [Display(Name = "Configuration Key")]
        public string ConfigKey { get; set; }

        [Required]
        [StringLength(1000)]
        [Display(Name = "Configuration Value")]
        public string ConfigValue { get; set; }

        [StringLength(500)]
        [Display(Name = "Description")]
        public string Description { get; set; }

        [StringLength(50)]
        [Display(Name = "Category")]
        public string Category { get; set; } // Email, SMS, Payment, Security, etc.

        [Display(Name = "Is Encrypted")]
        public bool IsEncrypted { get; set; } = false;

        [Display(Name = "Modified Date")]
        public DateTime ModifiedDate { get; set; } = DateTime.Now;

        [Display(Name = "Modified By")]
        public int ModifiedBy { get; set; }

        // Navigation Properties
        [ForeignKey("ModifiedBy")]
        public virtual User ModifiedByUser { get; set; }
    }

    // Payment Model
    [Table("Payments")]
    public class Payment
    {
        [Key]
        public int PaymentId { get; set; }

        [Required]
        [Display(Name = "Order ID")]
        public int OrderId { get; set; }

        [Required]
        [StringLength(100)]
        [Display(Name = "Transaction ID")]
        public string TransactionId { get; set; }

        [Required]
        [Display(Name = "Amount")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal Amount { get; set; }

        [Required]
        [StringLength(10)]
        [Display(Name = "Currency")]
        public string Currency { get; set; } = "USD";

        [Required]
        [StringLength(50)]
        [Display(Name = "Payment Method")]
        public string PaymentMethod { get; set; }

        [Required]
        [StringLength(20)]
        [Display(Name = "Status")]
        public string Status { get; set; }

        [Display(Name = "Payment Date")]
        public DateTime PaymentDate { get; set; } = DateTime.Now;

        [StringLength(1000)]
        [Display(Name = "Gateway Response")]
        public string GatewayResponse { get; set; }

        // Navigation Properties
        [ForeignKey("OrderId")]
        public virtual Order Order { get; set; }
    }

    // Refund Model
    [Table("Refunds")]
    public class Refund
    {
        [Key]
        public int RefundId { get; set; }

        [Required]
        [Display(Name = "Payment ID")]
        public int PaymentId { get; set; }

        [Required]
        [StringLength(100)]
        [Display(Name = "Refund Transaction ID")]
        public string RefundTransactionId { get; set; }

        [Required]
        [Display(Name = "Amount")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal Amount { get; set; }

        [Required]
        [StringLength(500)]
        [Display(Name = "Reason")]
        public string Reason { get; set; }

        [Required]
        [StringLength(20)]
        [Display(Name = "Status")]
        public string Status { get; set; }

        [Display(Name = "Refund Date")]
        public DateTime RefundDate { get; set; } = DateTime.Now;

        // Navigation Properties
        [ForeignKey("PaymentId")]
        public virtual Payment Payment { get; set; }
    }

    // Backup History Model
    [Table("BackupHistory")]
    public class BackupHistory
    {
        [Key]
        public int BackupId { get; set; }

        [Required]
        [StringLength(200)]
        [Display(Name = "Backup Name")]
        public string BackupName { get; set; }

        [Required]
        [StringLength(50)]
        [Display(Name = "Backup Type")]
        public string BackupType { get; set; }

        [StringLength(500)]
        [Display(Name = "Description")]
        public string Description { get; set; }

        [Required]
        [StringLength(500)]
        [Display(Name = "File Path")]
        public string FilePath { get; set; }

        [Display(Name = "File Size")]
        public long FileSize { get; set; }

        [Required]
        [StringLength(20)]
        [Display(Name = "Status")]
        public string Status { get; set; }

        [Display(Name = "Created Date")]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        [Display(Name = "Start Time")]
        public DateTime? StartTime { get; set; }

        [Display(Name = "End Time")]
        public DateTime? EndTime { get; set; }

        [Display(Name = "Created By")]
        public int CreatedBy { get; set; }

        // Navigation Properties
        [ForeignKey("CreatedBy")]
        public virtual User CreatedByUser { get; set; }
    }


}
