using System;
using System.Linq;
using System.Web.UI;
using System.Web.UI.WebControls;
using MediEase.DAL;
using MediEase.Models;
using MediEase.Utilities;

namespace MediEase
{
    public partial class Checkout : Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                LoadCheckoutData();
                CheckPrescriptionRequirement();
            }
        }

        private void LoadCheckoutData()
        {
            try
            {
                var currentUser = SecurityHelper.GetCurrentUser(true); // Load full details

                // Pre-fill user information if logged in
                if (currentUser != null)
                {
                    txtFirstName.Text = currentUser.FirstName ?? "";
                    txtLastName.Text = currentUser.LastName ?? "";
                    txtEmail.Text = currentUser.Email ?? "";
                    txtPhoneNumber.Text = currentUser.PhoneNumber ?? "";
                    txtAddress.Text = currentUser.Address ?? "";
                    txtCity.Text = currentUser.City ?? "";
                    txtZipCode.Text = currentUser.PostalCode ?? "";
                }

                LoadOrderSummary();
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error loading checkout data");
                ShowErrorMessage("Error loading checkout information. Please try again.");
            }
        }

        private void LoadOrderSummary()
        {
            var currentUser = SecurityHelper.GetCurrentUser();
            System.Collections.Generic.List<CartItem> cartItems;

            if (currentUser == null)
            {
                // Guest cart
                cartItems = Session["GuestCart"] as System.Collections.Generic.List<CartItem>;
                if (cartItems == null)
                {
                    Response.Redirect("~/Cart.aspx");
                    return;
                }

                // Load medicine details
                using (var db = new MediEaseContext())
                {
                    foreach (var item in cartItems)
                    {
                        item.Medicine = db.Medicines.Find(item.MedicineId);
                    }
                }
            }
            else
            {
                // Logged in user cart
                using (var db = new MediEaseContext())
                {
                    cartItems = db.CartItems
                        .Where(c => c.UserId == currentUser.UserId)
                        .ToList();

                    if (!cartItems.Any())
                    {
                        Response.Redirect("~/Cart.aspx");
                        return;
                    }
                }
            }

            // Bind order items
            rptOrderItems.DataSource = cartItems;
            rptOrderItems.DataBind();

            // Calculate totals
            CalculateOrderTotals(cartItems);
        }

        private void CalculateOrderTotals(System.Collections.Generic.IEnumerable<CartItem> cartItems)
        {
            decimal subtotal = cartItems.Sum(c => c.TotalPrice);
            decimal discount = cartItems.Sum(c => c.Medicine.DiscountAmount * c.Quantity);
            decimal tax = subtotal * 0.08m; // 8% tax
            decimal shipping = subtotal > 50 ? 0 : 5.99m; // Free shipping over $50
            decimal total = subtotal - discount + tax + shipping;

            lblSubtotal.Text = subtotal.ToString("N2");
            lblDiscount.Text = discount.ToString("N2");
            lblTax.Text = tax.ToString("N2");
            lblShipping.Text = shipping.ToString("N2");
            lblTotal.Text = total.ToString("N2");
        }

        private void CheckPrescriptionRequirement()
        {
            var currentUser = SecurityHelper.GetCurrentUser();
            bool requiresPrescription = false;

            if (currentUser == null)
            {
                // Check guest cart
                var guestCart = Session["GuestCart"] as System.Collections.Generic.List<CartItem>;
                if (guestCart != null)
                {
                    using (var db = new MediEaseContext())
                    {
                        requiresPrescription = guestCart.Any(c => 
                            db.Medicines.Find(c.MedicineId)?.PrescriptionRequired == true);
                    }
                }
            }
            else
            {
                // Check logged in user cart
                using (var db = new MediEaseContext())
                {
                    requiresPrescription = db.CartItems
                        .Where(c => c.UserId == currentUser.UserId)
                        .Any(c => c.Medicine.PrescriptionRequired);
                }
            }

            pnlPrescriptionUpload.Visible = requiresPrescription;
        }

        protected void btnPlaceOrder_Click(object sender, EventArgs e)
        {
            try
            {
                if (!ValidateOrderForm())
                    return;

                var currentUser = SecurityHelper.GetCurrentUser();
                var isGuestOrder = (currentUser == null);

                using (var db = new MediEaseContext())
                {
                    // Create or get user for guest orders
                    User orderUser;
                    if (isGuestOrder)
                    {
                        orderUser = CreateGuestUser(db);
                    }
                    else
                    {
                        orderUser = db.Users.Find(currentUser.UserId);
                    }

                    // Create order
                    var order = CreateOrder(db, orderUser);
                    
                    // Add order items
                    AddOrderItems(db, order);
                    
                    // Process payment (simplified for demo)
                    ProcessPayment(order);
                    
                    // Save order
                    db.SaveChanges();
                    
                    // Clear cart
                    ClearCart(orderUser);
                    
                    // Redirect to order confirmation
                    Response.Redirect($"~/OrderConfirmation.aspx?orderId={order.OrderId}");
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error placing order");
                ShowErrorMessage("Error placing order. Please try again.");
            }
        }

        private bool ValidateOrderForm()
        {
            bool isValid = true;

            // Basic validation
            if (string.IsNullOrWhiteSpace(txtFirstName.Text) ||
                string.IsNullOrWhiteSpace(txtLastName.Text) ||
                string.IsNullOrWhiteSpace(txtEmail.Text) ||
                string.IsNullOrWhiteSpace(txtPhoneNumber.Text) ||
                string.IsNullOrWhiteSpace(txtAddress.Text) ||
                string.IsNullOrWhiteSpace(txtCity.Text) ||
                string.IsNullOrWhiteSpace(txtZipCode.Text))
            {
                ShowErrorMessage("Please fill in all required fields.");
                isValid = false;
            }

            // Prescription validation
            if (pnlPrescriptionUpload.Visible && !fuPrescription.HasFile)
            {
                ShowErrorMessage("Please upload a prescription for prescription medicines.");
                isValid = false;
            }

            // Payment validation
            if (rblPaymentMethod.SelectedValue == "CreditCard")
            {
                if (string.IsNullOrWhiteSpace(txtCardNumber.Text) ||
                    string.IsNullOrWhiteSpace(txtExpiryDate.Text) ||
                    string.IsNullOrWhiteSpace(txtCVV.Text) ||
                    string.IsNullOrWhiteSpace(txtCardholderName.Text))
                {
                    ShowErrorMessage("Please fill in all credit card information.");
                    isValid = false;
                }
            }

            return isValid;
        }

        private User CreateGuestUser(MediEaseContext db)
        {
            var guestUser = new User
            {
                FirstName = txtFirstName.Text.Trim(),
                LastName = txtLastName.Text.Trim(),
                Email = txtEmail.Text.Trim(),
                PhoneNumber = txtPhoneNumber.Text.Trim(),
                Address = txtAddress.Text.Trim(),
                City = txtCity.Text.Trim(),
                PostalCode = txtZipCode.Text.Trim(),
                Role = "Guest",
                PasswordHash = "", // Guest users don't have passwords
                IsActive = false, // Guest users are not active accounts
                IsEmailVerified = false
            };

            db.Users.Add(guestUser);
            db.SaveChanges(); // Save to get the UserId

            return guestUser;
        }

        private Order CreateOrder(MediEaseContext db, User user)
        {
            var order = new Order
            {
                OrderNumber = GenerateOrderNumber(),
                CustomerId = user.UserId,
                OrderDate = DateTime.Now,
                Status = "Pending",
                PaymentMethod = rblPaymentMethod.SelectedValue,
                PaymentStatus = "Pending",
                ShippingAddress = $"{txtAddress.Text}, {txtCity.Text}, {ddlState.SelectedValue} {txtZipCode.Text}",
                SpecialInstructions = txtOrderNotes.Text.Trim(),
                Subtotal = Convert.ToDecimal(lblSubtotal.Text),
                DiscountAmount = Convert.ToDecimal(lblDiscount.Text),
                TaxAmount = Convert.ToDecimal(lblTax.Text),
                ShippingCost = Convert.ToDecimal(lblShipping.Text),
                TotalAmount = Convert.ToDecimal(lblTotal.Text)
            };

            db.Orders.Add(order);
            return order;
        }

        private void AddOrderItems(MediEaseContext db, Order order)
        {
            var currentUser = SecurityHelper.GetCurrentUser();
            System.Collections.Generic.List<CartItem> cartItems;

            if (currentUser == null)
            {
                cartItems = Session["GuestCart"] as System.Collections.Generic.List<CartItem>;
            }
            else
            {
                cartItems = db.CartItems.Where(c => c.UserId == currentUser.UserId).ToList();
            }

            foreach (var cartItem in cartItems)
            {
                var orderItem = new OrderItem
                {
                    OrderId = order.OrderId,
                    MedicineId = cartItem.MedicineId,
                    Quantity = cartItem.Quantity,
                    UnitPrice = cartItem.UnitPrice,
                    TotalPrice = cartItem.TotalPrice
                };

                db.OrderItems.Add(orderItem);
            }
        }

        private void ProcessPayment(Order order)
        {
            try
            {
                switch (order.PaymentMethod)
                {
                    case "CreditCard":
                        ProcessCreditCardPayment(order);
                        break;
                    case "PayPal":
                        ProcessPayPalPayment(order);
                        break;
                    case "CashOnDelivery":
                        ProcessCashOnDeliveryPayment(order);
                        break;
                    default:
                        throw new Exception("Invalid payment method selected");
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "ProcessPayment");
                order.PaymentStatus = "Failed";
                order.Status = "Payment Failed";
                throw;
            }
        }

        private void ProcessCreditCardPayment(Order order)
        {
            try
            {
                // Validate credit card information
                if (!ValidateCreditCard())
                {
                    throw new Exception("Invalid credit card information");
                }

                // Use PaymentService for actual processing
                var paymentResult = PaymentService.ProcessCreditCardPayment(new PaymentRequest
                {
                    Amount = order.TotalAmount,
                    CardNumber = txtCardNumber.Text.Trim(),
                    ExpiryDate = txtExpiryDate.Text.Trim(),
                    CVV = txtCVV.Text.Trim(),
                    CardholderName = txtCardholderName.Text.Trim(),
                    OrderNumber = order.OrderNumber,
                    CustomerEmail = txtEmail.Text.Trim()
                });

                if (paymentResult.IsSuccess)
                {
                    order.PaymentStatus = "Paid";
                    order.Status = "Processing";
                    order.PaymentTransactionId = paymentResult.TransactionId;
                    order.PaymentDate = DateTime.Now;
                }
                else
                {
                    throw new Exception($"Payment failed: {paymentResult.ErrorMessage}");
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "ProcessCreditCardPayment");
                throw;
            }
        }

        private void ProcessPayPalPayment(Order order)
        {
            try
            {
                // For PayPal, we would typically redirect to PayPal
                // For demo purposes, we'll simulate success
                var paymentResult = PaymentService.ProcessPayPalPayment(new PaymentRequest
                {
                    Amount = order.TotalAmount,
                    OrderNumber = order.OrderNumber,
                    CustomerEmail = txtEmail.Text.Trim(),
                    ReturnUrl = Request.Url.GetLeftPart(UriPartial.Authority) + "/OrderConfirmation.aspx",
                    CancelUrl = Request.Url.GetLeftPart(UriPartial.Authority) + "/Checkout.aspx"
                });

                if (paymentResult.IsSuccess)
                {
                    order.PaymentStatus = "Paid";
                    order.Status = "Processing";
                    order.PaymentTransactionId = paymentResult.TransactionId;
                    order.PaymentDate = DateTime.Now;
                }
                else
                {
                    throw new Exception($"PayPal payment failed: {paymentResult.ErrorMessage}");
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "ProcessPayPalPayment");
                throw;
            }
        }

        private void ProcessCashOnDeliveryPayment(Order order)
        {
            // Add COD charges
            order.TotalAmount += 2.99m; // COD fee
            order.PaymentStatus = "Pending";
            order.Status = "Processing";
            order.PaymentDate = null; // Will be set when payment is received
        }

        private bool ValidateCreditCard()
        {
            // Basic credit card validation
            var cardNumber = txtCardNumber.Text.Replace(" ", "").Replace("-", "");

            // Check if card number is numeric and has valid length
            if (!long.TryParse(cardNumber, out _) || cardNumber.Length < 13 || cardNumber.Length > 19)
            {
                ShowErrorMessage("Invalid card number format");
                return false;
            }

            // Validate expiry date
            if (!ValidateExpiryDate(txtExpiryDate.Text))
            {
                ShowErrorMessage("Invalid or expired card");
                return false;
            }

            // Validate CVV
            if (!int.TryParse(txtCVV.Text, out _) || txtCVV.Text.Length < 3 || txtCVV.Text.Length > 4)
            {
                ShowErrorMessage("Invalid CVV");
                return false;
            }

            return true;
        }

        private bool ValidateExpiryDate(string expiryDate)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(expiryDate) || !expiryDate.Contains("/"))
                    return false;

                var parts = expiryDate.Split('/');
                if (parts.Length != 2)
                    return false;

                if (!int.TryParse(parts[0], out int month) || !int.TryParse(parts[1], out int year))
                    return false;

                // Convert 2-digit year to 4-digit
                if (year < 100)
                    year += 2000;

                var expiryDateTime = new DateTime(year, month, 1).AddMonths(1).AddDays(-1);
                return expiryDateTime >= DateTime.Now.Date;
            }
            catch
            {
                return false;
            }
        }

        private void ClearCart(User user)
        {
            var currentUser = SecurityHelper.GetCurrentUser();

            if (currentUser == null)
            {
                // Clear guest cart
                Session["GuestCart"] = null;
            }
            else
            {
                // Clear logged in user cart
                using (var db = new MediEaseContext())
                {
                    var cartItems = db.CartItems.Where(c => c.UserId == user.UserId);
                    db.CartItems.RemoveRange(cartItems);
                    db.SaveChanges();
                }
            }
        }

        private string GenerateOrderNumber()
        {
            return "ORD" + DateTime.Now.ToString("yyyyMMddHHmmss") + new Random().Next(100, 999);
        }

        private void ShowErrorMessage(string message)
        {
            var master = Master as SiteMaster;
            master?.ShowErrorMessage(message);
        }

        private void ShowSuccessMessage(string message)
        {
            var master = Master as SiteMaster;
            master?.ShowSuccessMessage(message);
        }
    }
}
