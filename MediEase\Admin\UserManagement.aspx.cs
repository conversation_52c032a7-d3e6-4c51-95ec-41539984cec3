using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Services;
using System.Web.UI;
using System.Web.UI.WebControls;
using MediEase.DAL;
using MediEase.Models;
using MediEase.Utilities;

namespace MediEase.Admin
{
    public partial class UserManagement : Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            // Ensure user is authenticated and is an admin
            var currentUser = SecurityHelper.GetCurrentUser();
            if (currentUser == null)
            {
                Response.Redirect("~/Login.aspx?ReturnUrl=" + Server.UrlEncode(Request.Url.PathAndQuery));
                return;
            }

            if (!SecurityHelper.IsAdmin())
            {
                Response.Redirect("~/Default.aspx");
                return;
            }

            if (!IsPostBack)
            {
                LoadUsers();
                UpdateUserStats();
            }
        }

        private void LoadUsers()
        {
            try
            {
                var roleFilter = ddlRoleFilter.SelectedValue;
                var statusFilter = ddlStatusFilter.SelectedValue;
                var emailVerifiedFilter = ddlEmailVerified.SelectedValue;
                var searchTerm = txtSearch.Text.Trim();

                using (var db = new MediEaseContext())
                {
                    var query = db.Users.AsQueryable();

                    // Apply role filter
                    if (!string.IsNullOrEmpty(roleFilter))
                    {
                        query = query.Where(u => u.Role == roleFilter);
                    }

                    // Apply status filter
                    if (!string.IsNullOrEmpty(statusFilter))
                    {
                        switch (statusFilter)
                        {
                            case "Active":
                                query = query.Where(u => u.IsActive);
                                break;
                            case "Inactive":
                                query = query.Where(u => !u.IsActive);
                                break;
                            case "Locked":
                                query = query.Where(u => u.AccountLockedUntil.HasValue && u.AccountLockedUntil > DateTime.Now);
                                break;
                        }
                    }

                    // Apply email verified filter
                    if (!string.IsNullOrEmpty(emailVerifiedFilter))
                    {
                        var isVerified = bool.Parse(emailVerifiedFilter);
                        query = query.Where(u => u.IsEmailVerified == isVerified);
                    }

                    // Apply search filter
                    if (!string.IsNullOrEmpty(searchTerm))
                    {
                        query = query.Where(u => 
                            u.FirstName.Contains(searchTerm) ||
                            u.LastName.Contains(searchTerm) ||
                            u.Email.Contains(searchTerm) ||
                            u.PhoneNumber.Contains(searchTerm));
                    }

                    var users = query
                        .OrderBy(u => u.Role)
                        .ThenBy(u => u.LastName)
                        .ThenBy(u => u.FirstName)
                        .Select(u => new
                        {
                            u.UserId,
                            u.FirstName,
                            u.LastName,
                            FullName = u.FirstName + " " + u.LastName,
                            u.Email,
                            u.PhoneNumber,
                            u.Role,
                            u.IsActive,
                            u.IsEmailVerified,
                            u.IsPhoneVerified,
                            u.TwoFactorEnabled,
                            u.LastLogin,
                            u.CreatedDate,
                            u.ProfileImagePath,
                            IsAccountLocked = u.AccountLockedUntil.HasValue && u.AccountLockedUntil > DateTime.Now
                        })
                        .ToList();

                    if (users.Any())
                    {
                        gvUsers.DataSource = users;
                        gvUsers.DataBind();
                        
                        rptUsersCard.DataSource = users;
                        rptUsersCard.DataBind();

                        pnlNoUsers.Visible = false;
                    }
                    else
                    {
                        gvUsers.DataSource = null;
                        gvUsers.DataBind();
                        
                        rptUsersCard.DataSource = null;
                        rptUsersCard.DataBind();

                        pnlNoUsers.Visible = true;
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error loading users for management");
                ShowErrorMessage("Error loading users. Please try again.");
            }
        }

        private void UpdateUserStats()
        {
            try
            {
                using (var db = new MediEaseContext())
                {
                    var totalUsers = db.Users.Count();
                    var activeCustomers = db.Users.Count(u => u.Role == "Customer" && u.IsActive);
                    var activePharmacists = db.Users.Count(u => u.Role == "Pharmacist" && u.IsActive);
                    var activeAdmins = db.Users.Count(u => u.Role == "Admin" && u.IsActive);

                    lblTotalUsers.Text = totalUsers.ToString();
                    lblActiveCustomers.Text = activeCustomers.ToString();
                    lblActivePharmacists.Text = activePharmacists.ToString();
                    lblActiveAdmins.Text = activeAdmins.ToString();
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error updating user stats");
            }
        }

        protected void ddlRoleFilter_SelectedIndexChanged(object sender, EventArgs e)
        {
            LoadUsers();
        }

        protected void ddlStatusFilter_SelectedIndexChanged(object sender, EventArgs e)
        {
            LoadUsers();
        }

        protected void ddlEmailVerified_SelectedIndexChanged(object sender, EventArgs e)
        {
            LoadUsers();
        }

        protected void btnSearch_Click(object sender, EventArgs e)
        {
            LoadUsers();
        }

        protected void btnRefresh_Click(object sender, EventArgs e)
        {
            // Clear filters and reload
            ddlRoleFilter.SelectedValue = "";
            ddlStatusFilter.SelectedValue = "";
            ddlEmailVerified.SelectedValue = "";
            txtSearch.Text = "";
            LoadUsers();
            UpdateUserStats();
        }

        protected void gvUsers_RowCommand(object sender, GridViewCommandEventArgs e)
        {
            if (int.TryParse(e.CommandArgument.ToString(), out int userId))
            {
                switch (e.CommandName)
                {
                    case "Activate":
                        ToggleUserStatus(userId, true);
                        break;
                    case "Deactivate":
                        ToggleUserStatus(userId, false);
                        break;
                    case "Unlock":
                        UnlockUserAccount(userId);
                        break;
                }
            }
        }

        private void ToggleUserStatus(int userId, bool isActive)
        {
            try
            {
                var currentUser = SecurityHelper.GetCurrentUser();
                if (currentUser == null) return;

                using (var db = new MediEaseContext())
                {
                    var user = db.Users.Find(userId);
                    if (user != null)
                    {
                        // Prevent admin from deactivating themselves
                        if (user.UserId == currentUser.UserId && !isActive)
                        {
                            ShowErrorMessage("You cannot deactivate your own account.");
                            return;
                        }

                        user.IsActive = isActive;
                        user.ModifiedDate = DateTime.Now;
                        user.ModifiedBy = currentUser.UserId;

                        db.SaveChanges();

                        var action = isActive ? "activated" : "deactivated";
                        ErrorLogger.LogUserActivity($"User {user.Email} {action} by admin", currentUser.UserId);
                        ShowSuccessMessage($"User {action} successfully!");
                        
                        LoadUsers();
                        UpdateUserStats();
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error toggling user status");
                ShowErrorMessage("Error updating user status. Please try again.");
            }
        }

        private void UnlockUserAccount(int userId)
        {
            try
            {
                var currentUser = SecurityHelper.GetCurrentUser();
                if (currentUser == null) return;

                using (var db = new MediEaseContext())
                {
                    var user = db.Users.Find(userId);
                    if (user != null)
                    {
                        user.AccountLockedUntil = null;
                        user.FailedLoginAttempts = 0;
                        user.ModifiedDate = DateTime.Now;
                        user.ModifiedBy = currentUser.UserId;

                        db.SaveChanges();

                        ErrorLogger.LogUserActivity($"User account {user.Email} unlocked by admin", currentUser.UserId);
                        ShowSuccessMessage("User account unlocked successfully!");
                        
                        LoadUsers();
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error unlocking user account");
                ShowErrorMessage("Error unlocking user account. Please try again.");
            }
        }

        [WebMethod]
        public static object GetUserDetails(int userId)
        {
            try
            {
                var currentUser = SecurityHelper.GetCurrentUser();
                if (currentUser == null || !SecurityHelper.IsAdmin())
                {
                    return new { success = false, message = "Unauthorized access" };
                }

                using (var db = new MediEaseContext())
                {
                    var user = db.Users.Find(userId);
                    if (user == null)
                    {
                        return new { success = false, message = "User not found" };
                    }

                    var html = GenerateUserDetailsHtml(user, db);
                    var actions = GenerateUserActionsHtml(user);
                    return new { success = true, html = html, actions = actions };
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error getting user details");
                return new { success = false, message = "Error loading user details" };
            }
        }

        [WebMethod]
        public static object GetEditUserForm(int userId)
        {
            try
            {
                var currentUser = SecurityHelper.GetCurrentUser();
                if (currentUser == null || !SecurityHelper.IsAdmin())
                {
                    return new { success = false, message = "Unauthorized access" };
                }

                using (var db = new MediEaseContext())
                {
                    var user = db.Users.Find(userId);
                    if (user == null)
                    {
                        return new { success = false, message = "User not found" };
                    }

                    var html = GenerateEditUserFormHtml(user);
                    return new { success = true, html = html };
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error getting edit user form");
                return new { success = false, message = "Error loading edit form" };
            }
        }

        private static string GenerateUserDetailsHtml(User user, MediEaseContext db)
        {
            // Get user statistics
            var orderCount = 0;
            var prescriptionCount = 0;
            var totalSpent = 0m;

            if (user.Role == "Customer")
            {
                orderCount = db.Orders.Count(o => o.CustomerId == user.UserId);
                prescriptionCount = db.Prescriptions.Count(p => p.PatientId == user.UserId);
                totalSpent = db.Orders.Where(o => o.CustomerId == user.UserId && o.PaymentStatus == "Paid")
                    .Sum(o => (decimal?)o.TotalAmount) ?? 0;
            }

            return $@"
                <div class='row'>
                    <div class='col-md-4 text-center'>
                        <img src='{GetProfileImageStatic(user.ProfileImagePath)}' alt='Profile' class='rounded-circle mb-3' width='120' height='120' />
                        <h4>{user.FullName}</h4>
                        <span class='badge bg-{GetRoleColorStatic(user.Role)} fs-6'>{user.Role}</span>
                        {(user.IsActive ? "<span class='badge bg-success ms-2'>Active</span>" : "<span class='badge bg-danger ms-2'>Inactive</span>")}
                    </div>
                    <div class='col-md-8'>
                        <div class='row'>
                            <div class='col-md-6'>
                                <h6>Contact Information</h6>
                                <p><strong>Email:</strong> {user.Email} {(user.IsEmailVerified ? "<i class='fas fa-check-circle text-success'></i>" : "<i class='fas fa-exclamation-circle text-warning'></i>")}</p>
                                <p><strong>Phone:</strong> {user.PhoneNumber} {(user.IsPhoneVerified ? "<i class='fas fa-check-circle text-success'></i>" : "<i class='fas fa-exclamation-circle text-warning'></i>")}</p>
                                <p><strong>Address:</strong> {user.Address ?? "Not provided"}</p>
                                <p><strong>City:</strong> {user.City ?? "Not provided"}</p>
                                <p><strong>Country:</strong> {user.Country ?? "Not provided"}</p>
                            </div>
                            <div class='col-md-6'>
                                <h6>Account Information</h6>
                                <p><strong>User ID:</strong> {user.UserId}</p>
                                <p><strong>Date of Birth:</strong> {(user.DateOfBirth?.ToString("MM/dd/yyyy") ?? "Not provided")}</p>
                                <p><strong>Gender:</strong> {user.Gender ?? "Not specified"}</p>
                                <p><strong>Joined:</strong> {user.CreatedDate:MMM dd, yyyy}</p>
                                <p><strong>Last Login:</strong> {(user.LastLogin?.ToString("MMM dd, yyyy hh:mm tt") ?? "Never")}</p>
                                <p><strong>2FA:</strong> {(user.TwoFactorEnabled ? "Enabled" : "Disabled")}</p>
                            </div>
                        </div>";
        }

        private static string GenerateUserActionsHtml(User user)
        {
            var actions = new List<string>();

            if (user.IsActive)
            {
                actions.Add($"<button class='btn btn-warning' onclick='toggleUserStatus({user.UserId}, false)'>Deactivate User</button>");
            }
            else
            {
                actions.Add($"<button class='btn btn-success' onclick='toggleUserStatus({user.UserId}, true)'>Activate User</button>");
            }

            if (user.IsAccountLocked)
            {
                actions.Add($"<button class='btn btn-info' onclick='unlockUser({user.UserId})'>Unlock Account</button>");
            }

            actions.Add($"<button class='btn btn-primary' onclick='editUser({user.UserId})'>Edit User</button>");

            return string.Join(" ", actions);
        }

        private static string GenerateEditUserFormHtml(User user)
        {
            return $@"
                <form id='editUserForm'>
                    <input type='hidden' id='editUserId' value='{user.UserId}' />
                    <div class='row'>
                        <div class='col-md-6'>
                            <div class='mb-3'>
                                <label class='form-label'>First Name</label>
                                <input type='text' class='form-control' id='editFirstName' value='{user.FirstName}' required />
                            </div>
                        </div>
                        <div class='col-md-6'>
                            <div class='mb-3'>
                                <label class='form-label'>Last Name</label>
                                <input type='text' class='form-control' id='editLastName' value='{user.LastName}' required />
                            </div>
                        </div>
                    </div>
                    <div class='row'>
                        <div class='col-md-6'>
                            <div class='mb-3'>
                                <label class='form-label'>Email</label>
                                <input type='email' class='form-control' id='editEmail' value='{user.Email}' required />
                            </div>
                        </div>
                        <div class='col-md-6'>
                            <div class='mb-3'>
                                <label class='form-label'>Phone Number</label>
                                <input type='tel' class='form-control' id='editPhone' value='{user.PhoneNumber}' />
                            </div>
                        </div>
                    </div>
                    <div class='row'>
                        <div class='col-md-6'>
                            <div class='mb-3'>
                                <label class='form-label'>Role</label>
                                <select class='form-select' id='editRole' required>
                                    <option value='Customer' {(user.Role == "Customer" ? "selected" : "")}>Customer</option>
                                    <option value='Pharmacist' {(user.Role == "Pharmacist" ? "selected" : "")}>Pharmacist</option>
                                    <option value='Admin' {(user.Role == "Admin" ? "selected" : "")}>Admin</option>
                                </select>
                            </div>
                        </div>
                        <div class='col-md-6'>
                            <div class='mb-3'>
                                <label class='form-label'>Status</label>
                                <select class='form-select' id='editStatus'>
                                    <option value='true' {(user.IsActive ? "selected" : "")}>Active</option>
                                    <option value='false' {(!user.IsActive ? "selected" : "")}>Inactive</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class='d-grid'>
                        <button type='button' class='btn btn-primary' onclick='saveUserChanges()'>Save Changes</button>
                    </div>
                </form>";
        }

        // Helper methods for data binding
        protected string GetRoleColor(string role)
        {
            return GetRoleColorStatic(role);
        }

        private static string GetRoleColorStatic(string role)
        {
            switch (role?.ToLower())
            {
                case "admin": return "danger";
                case "pharmacist": return "info";
                case "customer": return "success";
                default: return "secondary";
            }
        }

        protected string GetProfileImage(object profileImagePath)
        {
            return GetProfileImageStatic(profileImagePath?.ToString());
        }

        private static string GetProfileImageStatic(string profileImagePath)
        {
            return string.IsNullOrEmpty(profileImagePath) ? "/Images/default-avatar.png" : profileImagePath;
        }

        private void ShowErrorMessage(string message)
        {
            var master = Master as SiteMaster;
            master?.ShowErrorMessage(message);
        }

        private void ShowSuccessMessage(string message)
        {
            var master = Master as SiteMaster;
            master?.ShowSuccessMessage(message);
        }

        protected override void OnPreRender(EventArgs e)
        {
            base.OnPreRender(e);
            
            // Set page metadata
            Page.Title = "User Management - MediEase Admin";
            
            var master = Master as SiteMaster;
            if (master != null)
            {
                master.AddMetaDescription("Manage users, customers, pharmacists, and admin accounts in the MediEase system.");
                master.AddMetaKeywords("user management, admin panel, customer management, pharmacist management, user accounts");
            }
        }
    }
}
