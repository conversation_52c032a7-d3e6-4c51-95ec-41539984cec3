-- Sample Data for MediEase Database

-- Insert Categories
INSERT INTO Categories (Name, Description) VALUES
('Pain Relief', 'Medications for pain management and relief'),
('Antibiotics', 'Antimicrobial medications for treating infections'),
('Vitamins & Supplements', 'Nutritional supplements and vitamins'),
('Cold & Flu', 'Medications for cold and flu symptoms'),
('Digestive Health', 'Medications for digestive and gastrointestinal issues'),
('Heart & Blood Pressure', 'Cardiovascular medications'),
('Diabetes Care', 'Medications and supplies for diabetes management'),
('Skin Care', 'Topical medications and skin care products'),
('Eye Care', 'Ophthalmic medications and eye care products'),
('Respiratory', 'Medications for respiratory conditions');

-- Insert Brands
INSERT INTO Brands (Name, Description) VALUES
('Pfizer', 'Leading pharmaceutical company'),
('Johnson & Johnson', 'Healthcare and pharmaceutical products'),
('Bayer', 'German pharmaceutical and life sciences company'),
('GSK', 'GlaxoSmithKline pharmaceutical company'),
('Novartis', 'Swiss multinational pharmaceutical company'),
('Roche', 'Swiss pharmaceutical company'),
('Merck', 'American pharmaceutical company'),
('AbbVie', 'Pharmaceutical research and development'),
('Bristol Myers Squibb', 'Biopharmaceutical company'),
('Sanofi', 'French pharmaceutical company');

-- Insert Sample Medicines
INSERT INTO Medicines (Name, GenericName, Description, CategoryId, BrandId, Price, DiscountPercentage, StockQuantity, PrescriptionRequired, Dosage, IsFeatured, AverageRating, ReviewCount, PurchaseCount) VALUES
('Tylenol Extra Strength', 'Acetaminophen', 'Fast-acting pain relief for headaches, muscle aches, and fever', 1, 2, 12.99, 10, 150, 0, '500mg tablets', 1, 4.5, 234, 1250),
('Advil Liqui-Gels', 'Ibuprofen', 'Fast pain relief in liquid-filled capsules', 1, 3, 15.49, 15, 200, 0, '200mg capsules', 1, 4.3, 189, 980),
('Amoxicillin', 'Amoxicillin', 'Broad-spectrum antibiotic for bacterial infections', 2, 1, 25.99, 0, 75, 1, '500mg capsules', 0, 4.7, 156, 450),
('Vitamin D3', 'Cholecalciferol', 'Essential vitamin for bone health and immune support', 3, 4, 18.99, 20, 300, 0, '2000 IU tablets', 1, 4.6, 312, 1100),
('Robitussin DM', 'Dextromethorphan', 'Cough suppressant and expectorant', 4, 2, 11.99, 5, 120, 0, '15ml syrup', 0, 4.2, 98, 670),
('Pepto-Bismol', 'Bismuth Subsalicylate', 'Relief from upset stomach, nausea, and diarrhea', 5, 3, 9.99, 0, 180, 0, '262mg tablets', 0, 4.4, 145, 890),
('Lisinopril', 'Lisinopril', 'ACE inhibitor for high blood pressure', 6, 1, 32.50, 0, 60, 1, '10mg tablets', 0, 4.8, 89, 320),
('Metformin', 'Metformin HCl', 'Medication for type 2 diabetes', 7, 5, 28.75, 0, 90, 1, '500mg tablets', 0, 4.6, 167, 540),
('Hydrocortisone Cream', 'Hydrocortisone', 'Topical anti-inflammatory for skin irritation', 8, 2, 8.99, 25, 250, 0, '1% cream', 0, 4.1, 203, 780),
('Visine Eye Drops', 'Tetrahydrozoline', 'Relief for red, irritated eyes', 9, 2, 6.99, 0, 400, 0, '0.5ml drops', 1, 4.0, 156, 1200),
('Albuterol Inhaler', 'Albuterol Sulfate', 'Bronchodilator for asthma and COPD', 10, 4, 45.99, 0, 40, 1, '90mcg inhaler', 0, 4.9, 78, 290),
('Aspirin 81mg', 'Acetylsalicylic Acid', 'Low-dose aspirin for heart health', 1, 3, 7.99, 0, 500, 0, '81mg tablets', 1, 4.3, 267, 1450),
('Omega-3 Fish Oil', 'EPA/DHA', 'Heart and brain health supplement', 3, 6, 24.99, 30, 200, 0, '1000mg softgels', 1, 4.5, 189, 890),
('Claritin', 'Loratadine', 'Non-drowsy allergy relief', 4, 3, 19.99, 10, 160, 0, '10mg tablets', 0, 4.4, 234, 1100),
('Prilosec OTC', 'Omeprazole', 'Acid reducer for heartburn relief', 5, 1, 22.99, 0, 140, 0, '20mg capsules', 0, 4.6, 178, 650);

-- Insert Sample Users
INSERT INTO Users (Username, Email, PasswordHash, FirstName, LastName, PhoneNumber, Role, IsActive, IsEmailVerified) VALUES
('admin', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'System', 'Administrator', '555-0001', 'Admin', 1, 1),
('pharmacist1', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Sarah', 'Johnson', '555-0002', 'Pharmacist', 1, 1),
('customer1', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'John', 'Doe', '555-0003', 'Customer', 1, 1),
('customer2', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Jane', 'Smith', '555-0004', 'Customer', 1, 1);

-- Insert Sample Orders
INSERT INTO Orders (UserId, OrderNumber, Status, TotalAmount, FinalAmount, PaymentMethod, PaymentStatus, ShippingAddress) VALUES
(3, 'ORD-2024-001', 'Delivered', 45.97, 45.97, 'Credit Card', 'Paid', '123 Main St, Anytown, ST 12345'),
(4, 'ORD-2024-002', 'Shipped', 32.98, 32.98, 'PayPal', 'Paid', '456 Oak Ave, Another City, ST 67890'),
(3, 'ORD-2024-003', 'Processing', 78.45, 78.45, 'Credit Card', 'Paid', '123 Main St, Anytown, ST 12345');

-- Insert Sample Order Items
INSERT INTO OrderItems (OrderId, MedicineId, Quantity, UnitPrice, TotalPrice) VALUES
(1, 1, 2, 12.99, 25.98),
(1, 5, 1, 11.99, 11.99),
(1, 6, 1, 9.99, 9.99),
(2, 2, 1, 15.49, 15.49),
(2, 10, 1, 6.99, 6.99),
(2, 9, 1, 8.99, 8.99),
(3, 4, 2, 18.99, 37.98),
(3, 12, 3, 7.99, 23.97),
(3, 13, 1, 24.99, 24.99);

-- Insert Sample Prescriptions
INSERT INTO Prescriptions (UserId, DoctorName, DoctorLicense, PrescriptionNumber, PrescriptionDate, Status) VALUES
(3, 'Dr. Michael Brown', 'MD12345', 'RX-2024-001', '2024-01-15', 'Verified'),
(4, 'Dr. Emily Davis', 'MD67890', 'RX-2024-002', '2024-01-20', 'Pending');

-- Insert Sample Prescription Items
INSERT INTO PrescriptionItems (PrescriptionId, MedicineId, Quantity, Dosage, Instructions, Duration) VALUES
(1, 3, 30, '500mg', 'Take one capsule twice daily with food', '10 days'),
(1, 7, 30, '10mg', 'Take one tablet daily in the morning', '30 days'),
(2, 11, 1, '90mcg', 'Use as needed for breathing difficulty', 'As needed');
