-- =============================================
-- MediEase Complete SQL Queries
-- All SQL queries required for the web application
-- =============================================

-- =============================================
-- 1. USER AUTHENTICATION AND MANAGEMENT
-- =============================================

-- User Registration
INSERT INTO Users (Email, PasswordHash, FirstName, LastName, PhoneNumber, Role, IsActive, IsEmailVerified, CreatedDate)
VALUES (@Email, @PasswordHash, @FirstName, @LastName, @PhoneNumber, 'Customer', 1, 0, GETDATE());

-- User Login Verification
SELECT UserId, Email, PasswordHash, FirstName, LastName, Role, IsActive, IsEmailVerified, LoyaltyPoints
FROM Users 
WHERE Email = @Email AND IsActive = 1;

-- Update Last Login Date
UPDATE Users 
SET LastLoginDate = GETDATE() 
WHERE UserId = @UserId;

-- Get User Profile
SELECT UserId, Email, FirstName, LastName, PhoneNumber, Address, City, State, PostalCode, 
       Country, DateOfBirth, Gender, LoyaltyPoints, ProfilePicture, Bio, CreatedDate
FROM Users 
WHERE UserId = @UserId;

-- Update User Profile
UPDATE Users 
SET FirstName = @FirstName, LastName = @LastName, PhoneNumber = @PhoneNumber,
    Address = @Address, City = @City, State = @State, PostalCode = @PostalCode,
    Country = @Country, DateOfBirth = @DateOfBirth, Gender = @Gender,
    ModifiedDate = GETDATE()
WHERE UserId = @UserId;

-- Password Reset Token
UPDATE Users 
SET PasswordResetToken = @Token, PasswordResetExpiry = DATEADD(HOUR, 24, GETDATE())
WHERE Email = @Email;

-- Verify Reset Token
SELECT UserId, Email 
FROM Users 
WHERE PasswordResetToken = @Token AND PasswordResetExpiry > GETDATE();

-- Update Password
UPDATE Users 
SET PasswordHash = @NewPasswordHash, PasswordResetToken = NULL, PasswordResetExpiry = NULL
WHERE UserId = @UserId;

-- Email Verification
UPDATE Users 
SET IsEmailVerified = 1, EmailVerificationToken = NULL
WHERE EmailVerificationToken = @Token;

-- Get All Users (Admin)
SELECT UserId, Email, FirstName, LastName, Role, IsActive, IsEmailVerified, CreatedDate, LastLoginDate
FROM Users 
ORDER BY CreatedDate DESC;

-- Update User Status (Admin)
UPDATE Users 
SET IsActive = @IsActive 
WHERE UserId = @UserId;

-- Update User Role (Admin)
UPDATE Users 
SET Role = @Role 
WHERE UserId = @UserId;

-- =============================================
-- 2. MEDICINE CATALOG MANAGEMENT
-- =============================================

-- Get All Categories
SELECT CategoryId, Name, Description, ImageUrl, IsActive 
FROM Categories 
WHERE IsActive = 1 
ORDER BY Name;

-- Get All Brands
SELECT BrandId, Name, Description, LogoUrl, Website, IsActive 
FROM Brands 
WHERE IsActive = 1 
ORDER BY Name;

-- Get Featured Medicines
SELECT m.MedicineId, m.Name, m.GenericName, m.Description, m.Price, m.DiscountPercentage,
       m.StockQuantity, m.PrescriptionRequired, m.Dosage, m.ImageUrl, m.AverageRating,
       m.ReviewCount, c.Name as CategoryName, b.Name as BrandName,
       (m.Price * (1 - m.DiscountPercentage / 100)) as FinalPrice
FROM Medicines m
INNER JOIN Categories c ON m.CategoryId = c.CategoryId
LEFT JOIN Brands b ON m.BrandId = b.BrandId
WHERE m.IsFeatured = 1 AND m.IsActive = 1 AND m.StockQuantity > 0
ORDER BY m.PurchaseCount DESC;

-- Search Medicines
SELECT m.MedicineId, m.Name, m.GenericName, m.Description, m.Price, m.DiscountPercentage,
       m.StockQuantity, m.PrescriptionRequired, m.Dosage, m.ImageUrl, m.AverageRating,
       m.ReviewCount, c.Name as CategoryName, b.Name as BrandName,
       (m.Price * (1 - m.DiscountPercentage / 100)) as FinalPrice
FROM Medicines m
INNER JOIN Categories c ON m.CategoryId = c.CategoryId
LEFT JOIN Brands b ON m.BrandId = b.BrandId
WHERE m.IsActive = 1 AND m.StockQuantity > 0
  AND (m.Name LIKE '%' + @SearchTerm + '%' 
       OR m.GenericName LIKE '%' + @SearchTerm + '%' 
       OR m.Description LIKE '%' + @SearchTerm + '%')
ORDER BY m.Name;

-- Get Medicines by Category
SELECT m.MedicineId, m.Name, m.GenericName, m.Description, m.Price, m.DiscountPercentage,
       m.StockQuantity, m.PrescriptionRequired, m.Dosage, m.ImageUrl, m.AverageRating,
       m.ReviewCount, c.Name as CategoryName, b.Name as BrandName,
       (m.Price * (1 - m.DiscountPercentage / 100)) as FinalPrice
FROM Medicines m
INNER JOIN Categories c ON m.CategoryId = c.CategoryId
LEFT JOIN Brands b ON m.BrandId = b.BrandId
WHERE m.CategoryId = @CategoryId AND m.IsActive = 1 AND m.StockQuantity > 0
ORDER BY m.Name;

-- Get Medicine Details
SELECT m.MedicineId, m.Name, m.GenericName, m.Description, m.Price, m.DiscountPercentage,
       m.StockQuantity, m.PrescriptionRequired, m.Dosage, m.DosageForm, m.Strength,
       m.ImageUrl, m.AverageRating, m.ReviewCount, m.PurchaseCount,
       c.Name as CategoryName, b.Name as BrandName, b.Website as BrandWebsite,
       (m.Price * (1 - m.DiscountPercentage / 100)) as FinalPrice
FROM Medicines m
INNER JOIN Categories c ON m.CategoryId = c.CategoryId
LEFT JOIN Brands b ON m.BrandId = b.BrandId
WHERE m.MedicineId = @MedicineId AND m.IsActive = 1;

-- Add New Medicine (Admin/Pharmacist)
INSERT INTO Medicines (Name, GenericName, Description, CategoryId, BrandId, Price, 
                      StockQuantity, PrescriptionRequired, Dosage, DosageForm, Strength,
                      ImageUrl, IsActive, CreatedDate, CreatedBy)
VALUES (@Name, @GenericName, @Description, @CategoryId, @BrandId, @Price,
        @StockQuantity, @PrescriptionRequired, @Dosage, @DosageForm, @Strength,
        @ImageUrl, 1, GETDATE(), @CreatedBy);

-- Update Medicine
UPDATE Medicines 
SET Name = @Name, GenericName = @GenericName, Description = @Description,
    CategoryId = @CategoryId, BrandId = @BrandId, Price = @Price,
    StockQuantity = @StockQuantity, PrescriptionRequired = @PrescriptionRequired,
    Dosage = @Dosage, DosageForm = @DosageForm, Strength = @Strength,
    ImageUrl = @ImageUrl, ModifiedDate = GETDATE(), ModifiedBy = @ModifiedBy
WHERE MedicineId = @MedicineId;

-- Update Stock Quantity
UPDATE Medicines 
SET StockQuantity = @NewQuantity, ModifiedDate = GETDATE()
WHERE MedicineId = @MedicineId;

-- Get Low Stock Medicines
SELECT m.MedicineId, m.Name, m.StockQuantity, m.MinStockLevel, c.Name as CategoryName
FROM Medicines m
INNER JOIN Categories c ON m.CategoryId = c.CategoryId
WHERE m.StockQuantity <= m.MinStockLevel AND m.IsActive = 1
ORDER BY m.StockQuantity ASC;

-- =============================================
-- 3. SHOPPING CART MANAGEMENT
-- =============================================

-- Add Item to Cart
INSERT INTO ShoppingCart (UserId, MedicineId, Quantity, AddedDate)
VALUES (@UserId, @MedicineId, @Quantity, GETDATE())
ON DUPLICATE KEY UPDATE 
Quantity = Quantity + @Quantity, ModifiedDate = GETDATE();

-- Alternative for SQL Server (no ON DUPLICATE KEY)
IF EXISTS (SELECT 1 FROM ShoppingCart WHERE UserId = @UserId AND MedicineId = @MedicineId)
    UPDATE ShoppingCart 
    SET Quantity = Quantity + @Quantity, ModifiedDate = GETDATE()
    WHERE UserId = @UserId AND MedicineId = @MedicineId;
ELSE
    INSERT INTO ShoppingCart (UserId, MedicineId, Quantity, AddedDate)
    VALUES (@UserId, @MedicineId, @Quantity, GETDATE());

-- Get Cart Items
SELECT sc.CartId, sc.UserId, sc.MedicineId, sc.Quantity, sc.AddedDate,
       m.Name, m.GenericName, m.Price, m.DiscountPercentage, m.ImageUrl,
       m.StockQuantity, m.PrescriptionRequired,
       (m.Price * (1 - m.DiscountPercentage / 100)) as UnitPrice,
       (sc.Quantity * m.Price * (1 - m.DiscountPercentage / 100)) as TotalPrice
FROM ShoppingCart sc
INNER JOIN Medicines m ON sc.MedicineId = m.MedicineId
WHERE sc.UserId = @UserId AND m.IsActive = 1
ORDER BY sc.AddedDate DESC;

-- Update Cart Item Quantity
UPDATE ShoppingCart 
SET Quantity = @Quantity, ModifiedDate = GETDATE()
WHERE CartId = @CartId AND UserId = @UserId;

-- Remove Item from Cart
DELETE FROM ShoppingCart 
WHERE CartId = @CartId AND UserId = @UserId;

-- Clear Cart
DELETE FROM ShoppingCart 
WHERE UserId = @UserId;

-- Get Cart Summary
SELECT COUNT(*) as ItemCount, 
       SUM(sc.Quantity * m.Price * (1 - m.DiscountPercentage / 100)) as TotalAmount
FROM ShoppingCart sc
INNER JOIN Medicines m ON sc.MedicineId = m.MedicineId
WHERE sc.UserId = @UserId AND m.IsActive = 1;

-- =============================================
-- 4. ORDER MANAGEMENT
-- =============================================

-- Create New Order
INSERT INTO Orders (OrderNumber, CustomerId, Status, OrderDate, Subtotal, TaxAmount, 
                   ShippingCost, DiscountAmount, TotalAmount, PaymentMethod, PaymentStatus,
                   ShippingAddress, ShippingCity, ShippingPostalCode, ContactPhone, 
                   ContactEmail, RequiresPrescription, CreatedDate)
VALUES (@OrderNumber, @CustomerId, 'Pending', GETDATE(), @Subtotal, @TaxAmount,
        @ShippingCost, @DiscountAmount, @TotalAmount, @PaymentMethod, 'Pending',
        @ShippingAddress, @ShippingCity, @ShippingPostalCode, @ContactPhone,
        @ContactEmail, @RequiresPrescription, GETDATE());

-- Get Last Inserted Order ID
SELECT SCOPE_IDENTITY() as OrderId;

-- Add Order Items
INSERT INTO OrderItems (OrderId, MedicineId, Quantity, UnitPrice, TotalPrice, CreatedDate)
VALUES (@OrderId, @MedicineId, @Quantity, @UnitPrice, @TotalPrice, GETDATE());

-- Get Customer Orders
SELECT o.OrderId, o.OrderNumber, o.Status, o.OrderDate, o.TotalAmount, o.PaymentStatus,
       o.ExpectedDeliveryDate, o.ActualDeliveryDate, o.TrackingNumber,
       COUNT(oi.OrderItemId) as ItemCount
FROM Orders o
LEFT JOIN OrderItems oi ON o.OrderId = oi.OrderId
WHERE o.CustomerId = @CustomerId
GROUP BY o.OrderId, o.OrderNumber, o.Status, o.OrderDate, o.TotalAmount, o.PaymentStatus,
         o.ExpectedDeliveryDate, o.ActualDeliveryDate, o.TrackingNumber
ORDER BY o.OrderDate DESC;

-- Get Order Details
SELECT o.OrderId, o.OrderNumber, o.Status, o.OrderDate, o.Subtotal, o.TaxAmount,
       o.ShippingCost, o.DiscountAmount, o.TotalAmount, o.PaymentMethod, o.PaymentStatus,
       o.ShippingAddress, o.ShippingCity, o.ShippingPostalCode, o.ContactPhone,
       o.ContactEmail, o.TrackingNumber, o.CourierService, o.DeliveryInstructions,
       u.FirstName, u.LastName, u.Email
FROM Orders o
INNER JOIN Users u ON o.CustomerId = u.UserId
WHERE o.OrderId = @OrderId;

-- Get Order Items
SELECT oi.OrderItemId, oi.MedicineId, oi.Quantity, oi.UnitPrice, oi.TotalPrice,
       m.Name, m.GenericName, m.ImageUrl, m.PrescriptionRequired
FROM OrderItems oi
INNER JOIN Medicines m ON oi.MedicineId = m.MedicineId
WHERE oi.OrderId = @OrderId;

-- Update Order Status
UPDATE Orders 
SET Status = @Status, ModifiedDate = GETDATE()
WHERE OrderId = @OrderId;

-- Update Payment Status
UPDATE Orders 
SET PaymentStatus = @PaymentStatus, PaymentTransactionId = @TransactionId, 
    PaymentDate = GETDATE(), ModifiedDate = GETDATE()
WHERE OrderId = @OrderId;

-- Update Tracking Information
UPDATE Orders 
SET TrackingNumber = @TrackingNumber, CourierService = @CourierService,
    ModifiedDate = GETDATE()
WHERE OrderId = @OrderId;

-- Mark Order as Delivered
UPDATE Orders
SET Status = 'Delivered', ActualDeliveryDate = GETDATE(), ModifiedDate = GETDATE()
WHERE OrderId = @OrderId;

-- =============================================
-- 5. PRESCRIPTION MANAGEMENT
-- =============================================

-- Upload Prescription
INSERT INTO Prescriptions (UserId, PrescriptionNumber, DoctorName, DoctorLicense,
                          PrescriptionDate, ExpiryDate, OriginalFileName, FilePath,
                          FileSize, FileType, Status, CreatedDate)
VALUES (@UserId, @PrescriptionNumber, @DoctorName, @DoctorLicense,
        @PrescriptionDate, @ExpiryDate, @OriginalFileName, @FilePath,
        @FileSize, @FileType, 'Pending', GETDATE());

-- Get User Prescriptions
SELECT PrescriptionId, PrescriptionNumber, DoctorName, PrescriptionDate, ExpiryDate,
       Status, OriginalFileName, CreatedDate, VerificationDate
FROM Prescriptions
WHERE UserId = @UserId
ORDER BY CreatedDate DESC;

-- Get Prescription Details
SELECT p.PrescriptionId, p.UserId, p.PrescriptionNumber, p.DoctorName, p.DoctorLicense,
       p.PrescriptionDate, p.ExpiryDate, p.Diagnosis, p.Instructions, p.OriginalFileName,
       p.FilePath, p.Status, p.VerificationDate, p.VerificationNotes, p.CreatedDate,
       u.FirstName, u.LastName, u.Email, u.PhoneNumber
FROM Prescriptions p
INNER JOIN Users u ON p.UserId = u.UserId
WHERE p.PrescriptionId = @PrescriptionId;

-- Get Pending Prescriptions (Pharmacist)
SELECT p.PrescriptionId, p.PrescriptionNumber, p.DoctorName, p.PrescriptionDate,
       p.Status, p.CreatedDate, u.FirstName, u.LastName, u.Email
FROM Prescriptions p
INNER JOIN Users u ON p.UserId = u.UserId
WHERE p.Status = 'Pending'
ORDER BY p.CreatedDate ASC;

-- Verify Prescription
UPDATE Prescriptions
SET Status = @Status, VerifiedBy = @VerifiedBy, VerificationDate = GETDATE(),
    VerificationNotes = @VerificationNotes
WHERE PrescriptionId = @PrescriptionId;

-- Reject Prescription
UPDATE Prescriptions
SET Status = 'Rejected', VerifiedBy = @VerifiedBy, VerificationDate = GETDATE(),
    RejectionReason = @RejectionReason
WHERE PrescriptionId = @PrescriptionId;

-- =============================================
-- 6. LOYALTY PROGRAM
-- =============================================

-- Add Loyalty Points (Order)
INSERT INTO LoyaltyTransactions (UserId, OrderId, TransactionType, Points, Description, CreatedDate)
VALUES (@UserId, @OrderId, 'EARNED', @Points, 'Points earned from order #' + @OrderNumber, GETDATE());

-- Update User Loyalty Points
UPDATE Users
SET LoyaltyPoints = LoyaltyPoints + @Points
WHERE UserId = @UserId;

-- Redeem Loyalty Points
INSERT INTO LoyaltyTransactions (UserId, OrderId, TransactionType, Points, Description, CreatedDate)
VALUES (@UserId, @OrderId, 'REDEEMED', -@Points, 'Points redeemed for order #' + @OrderNumber, GETDATE());

UPDATE Users
SET LoyaltyPoints = LoyaltyPoints - @Points
WHERE UserId = @UserId;

-- Get Loyalty Transaction History
SELECT TransactionId, TransactionType, Points, Description, CreatedDate
FROM LoyaltyTransactions
WHERE UserId = @UserId
ORDER BY CreatedDate DESC;

-- Get User Loyalty Points Balance
SELECT LoyaltyPoints
FROM Users
WHERE UserId = @UserId;

-- =============================================
-- 7. REVIEWS AND RATINGS
-- =============================================

-- Add Medicine Review
INSERT INTO MedicineReviews (MedicineId, UserId, OrderId, Rating, Title, ReviewText,
                            IsVerifiedPurchase, CreatedDate)
VALUES (@MedicineId, @UserId, @OrderId, @Rating, @Title, @ReviewText,
        @IsVerifiedPurchase, GETDATE());

-- Get Medicine Reviews
SELECT mr.ReviewId, mr.Rating, mr.Title, mr.ReviewText, mr.IsVerifiedPurchase,
       mr.CreatedDate, mr.HelpfulVotes, u.FirstName, u.LastName
FROM MedicineReviews mr
INNER JOIN Users u ON mr.UserId = u.UserId
WHERE mr.MedicineId = @MedicineId AND mr.IsApproved = 1
ORDER BY mr.CreatedDate DESC;

-- Update Medicine Rating
UPDATE Medicines
SET AverageRating = (
    SELECT AVG(CAST(Rating as DECIMAL(3,2)))
    FROM MedicineReviews
    WHERE MedicineId = @MedicineId AND IsApproved = 1
),
ReviewCount = (
    SELECT COUNT(*)
    FROM MedicineReviews
    WHERE MedicineId = @MedicineId AND IsApproved = 1
)
WHERE MedicineId = @MedicineId;

-- Approve Review (Admin)
UPDATE MedicineReviews
SET IsApproved = 1, ApprovedBy = @ApprovedBy, ApprovedDate = GETDATE()
WHERE ReviewId = @ReviewId;

-- =============================================
-- 8. INVENTORY MANAGEMENT
-- =============================================

-- Record Stock Movement
INSERT INTO StockMovements (MedicineId, MovementType, Quantity, PreviousStock, NewStock,
                           Reference, Notes, CreatedDate, CreatedBy)
VALUES (@MedicineId, @MovementType, @Quantity, @PreviousStock, @NewStock,
        @Reference, @Notes, GETDATE(), @CreatedBy);

-- Get Stock Movement History
SELECT sm.MovementId, sm.MovementType, sm.Quantity, sm.PreviousStock, sm.NewStock,
       sm.Reference, sm.Notes, sm.CreatedDate, u.FirstName + ' ' + u.LastName as CreatedBy
FROM StockMovements sm
LEFT JOIN Users u ON sm.CreatedBy = u.UserId
WHERE sm.MedicineId = @MedicineId
ORDER BY sm.CreatedDate DESC;

-- Get Inventory Report
SELECT m.MedicineId, m.Name, m.StockQuantity, m.MinStockLevel, m.ReorderLevel,
       c.Name as CategoryName, b.Name as BrandName,
       CASE
           WHEN m.StockQuantity <= 0 THEN 'Out of Stock'
           WHEN m.StockQuantity <= m.MinStockLevel THEN 'Low Stock'
           WHEN m.StockQuantity <= m.ReorderLevel THEN 'Reorder Required'
           ELSE 'In Stock'
       END as StockStatus
FROM Medicines m
INNER JOIN Categories c ON m.CategoryId = c.CategoryId
LEFT JOIN Brands b ON m.BrandId = b.BrandId
WHERE m.IsActive = 1
ORDER BY m.StockQuantity ASC;

-- =============================================
-- 9. REPORTING AND ANALYTICS
-- =============================================

-- Daily Sales Report
SELECT
    CAST(OrderDate as DATE) as SaleDate,
    COUNT(*) as TotalOrders,
    SUM(TotalAmount) as TotalRevenue,
    AVG(TotalAmount) as AverageOrderValue,
    COUNT(DISTINCT CustomerId) as UniqueCustomers
FROM Orders
WHERE Status NOT IN ('Cancelled')
  AND OrderDate >= @StartDate AND OrderDate <= @EndDate
GROUP BY CAST(OrderDate as DATE)
ORDER BY SaleDate DESC;

-- Top Selling Medicines
SELECT m.MedicineId, m.Name, m.GenericName,
       SUM(oi.Quantity) as TotalQuantitySold,
       SUM(oi.TotalPrice) as TotalRevenue,
       COUNT(DISTINCT oi.OrderId) as OrderCount
FROM OrderItems oi
INNER JOIN Medicines m ON oi.MedicineId = m.MedicineId
INNER JOIN Orders o ON oi.OrderId = o.OrderId
WHERE o.Status NOT IN ('Cancelled')
  AND o.OrderDate >= @StartDate AND o.OrderDate <= @EndDate
GROUP BY m.MedicineId, m.Name, m.GenericName
ORDER BY TotalQuantitySold DESC;

-- Customer Analytics
SELECT u.UserId, u.FirstName, u.LastName, u.Email,
       COUNT(o.OrderId) as TotalOrders,
       SUM(o.TotalAmount) as TotalSpent,
       AVG(o.TotalAmount) as AverageOrderValue,
       MAX(o.OrderDate) as LastOrderDate,
       u.LoyaltyPoints
FROM Users u
LEFT JOIN Orders o ON u.UserId = o.CustomerId AND o.Status NOT IN ('Cancelled')
WHERE u.Role = 'Customer'
GROUP BY u.UserId, u.FirstName, u.LastName, u.Email, u.LoyaltyPoints
ORDER BY TotalSpent DESC;

-- Monthly Revenue Report
SELECT
    YEAR(OrderDate) as Year,
    MONTH(OrderDate) as Month,
    DATENAME(MONTH, OrderDate) as MonthName,
    COUNT(*) as TotalOrders,
    SUM(TotalAmount) as TotalRevenue,
    AVG(TotalAmount) as AverageOrderValue
FROM Orders
WHERE Status NOT IN ('Cancelled')
GROUP BY YEAR(OrderDate), MONTH(OrderDate), DATENAME(MONTH, OrderDate)
ORDER BY Year DESC, Month DESC;

-- =============================================
-- 10. SEARCH AND FILTERING
-- =============================================

-- Advanced Medicine Search
SELECT m.MedicineId, m.Name, m.GenericName, m.Description, m.Price, m.DiscountPercentage,
       m.StockQuantity, m.PrescriptionRequired, m.Dosage, m.ImageUrl, m.AverageRating,
       c.Name as CategoryName, b.Name as BrandName,
       (m.Price * (1 - m.DiscountPercentage / 100)) as FinalPrice
FROM Medicines m
INNER JOIN Categories c ON m.CategoryId = c.CategoryId
LEFT JOIN Brands b ON m.BrandId = b.BrandId
WHERE m.IsActive = 1 AND m.StockQuantity > 0
  AND (@SearchTerm IS NULL OR m.Name LIKE '%' + @SearchTerm + '%'
       OR m.GenericName LIKE '%' + @SearchTerm + '%')
  AND (@CategoryId IS NULL OR m.CategoryId = @CategoryId)
  AND (@BrandId IS NULL OR m.BrandId = @BrandId)
  AND (@MinPrice IS NULL OR m.Price >= @MinPrice)
  AND (@MaxPrice IS NULL OR m.Price <= @MaxPrice)
  AND (@PrescriptionRequired IS NULL OR m.PrescriptionRequired = @PrescriptionRequired)
ORDER BY
  CASE @SortBy
    WHEN 'name' THEN m.Name
    WHEN 'price_low' THEN CAST(m.Price as NVARCHAR)
    WHEN 'price_high' THEN CAST(m.Price as NVARCHAR)
    WHEN 'rating' THEN CAST(m.AverageRating as NVARCHAR)
    ELSE m.Name
  END;

-- =============================================
-- 11. NOTIFICATIONS
-- =============================================

-- Create Notification
INSERT INTO Notifications (UserId, Title, Message, NotificationType, RelatedEntityType,
                          RelatedEntityId, Priority, CreatedDate)
VALUES (@UserId, @Title, @Message, @NotificationType, @RelatedEntityType,
        @RelatedEntityId, @Priority, GETDATE());

-- Get User Notifications
SELECT NotificationId, Title, Message, NotificationType, IsRead, Priority,
       ActionUrl, CreatedDate
FROM Notifications
WHERE UserId = @UserId
ORDER BY CreatedDate DESC;

-- Mark Notification as Read
UPDATE Notifications
SET IsRead = 1, ReadDate = GETDATE()
WHERE NotificationId = @NotificationId AND UserId = @UserId;

-- Get Unread Notification Count
SELECT COUNT(*) as UnreadCount
FROM Notifications
WHERE UserId = @UserId AND IsRead = 0;

-- =============================================
-- 12. SYSTEM ADMINISTRATION
-- =============================================

-- Get System Settings
SELECT SettingKey, SettingValue, Description, Category
FROM SystemSettings
WHERE IsEditable = 1
ORDER BY Category, SettingKey;

-- Update System Setting
UPDATE SystemSettings
SET SettingValue = @SettingValue, ModifiedDate = GETDATE(), ModifiedBy = @ModifiedBy
WHERE SettingKey = @SettingKey;

-- Audit Log Entry
INSERT INTO AuditLogs (UserId, Action, EntityType, EntityId, OldValues, NewValues,
                      IPAddress, UserAgent, Timestamp)
VALUES (@UserId, @Action, @EntityType, @EntityId, @OldValues, @NewValues,
        @IPAddress, @UserAgent, GETDATE());

-- Error Log Entry
INSERT INTO ErrorLogs (ErrorMessage, StackTrace, Source, UserId, RequestUrl,
                      HttpMethod, IPAddress, UserAgent, Severity, CreatedDate)
VALUES (@ErrorMessage, @StackTrace, @Source, @UserId, @RequestUrl,
        @HttpMethod, @IPAddress, @UserAgent, @Severity, GETDATE());
