# 🚀 MediEase Production Deployment Guide

## 🔒 Security Checklist

### 1. **Remove Demo Content**
- ✅ Demo accounts removed from login page
- ✅ Documentation updated to remove demo credentials
- ✅ Database seeder updated for production use

### 2. **Update Default Credentials**
Before deploying to production, update these critical settings:

#### **Database Seeder (Required)**
Update the admin account in `DAL/MediEaseContext.cs`:
```csharp
Email = "<EMAIL>", // Change to your admin email
PasswordHash = BCrypt.Net.BCrypt.HashPassword("YourSecurePassword!"), // Use a strong password
```

#### **Configuration Settings**
Update `Web.config` for production:
```xml
<appSettings>
  <!-- Use your production OpenRouter API key -->
  <add key="OpenRouterApiKey" value="your-production-api-key" />
  
  <!-- Add encryption key for production -->
  <add key="EncryptionKey" value="YourSecure32CharacterEncryptionKey!" />
</appSettings>

<connectionStrings>
  <!-- Use production database connection -->
  <add name="MediEaseConnection" 
       connectionString="Data Source=your-server;Initial Catalog=MediEase;..." />
</connectionStrings>
```

### 3. **Database Setup**

#### **Production Database**
1. Create a new SQL Server database for production
2. Update the connection string in `Web.config`
3. Run the application to auto-create tables
4. The system will create an initial admin user automatically

#### **Manual Admin Creation (Alternative)**
If you prefer to create admin manually:
```sql
INSERT INTO Users (FirstName, LastName, Email, PasswordHash, Role, IsActive, IsEmailVerified)
VALUES ('Your', 'Name', '<EMAIL>', 
        '$2a$10$...', -- Use BCrypt to hash your password
        'Admin', 1, 1)
```

### 4. **Security Hardening**

#### **Web.config Security**
```xml
<system.web>
  <!-- Enable HTTPS only -->
  <httpCookies requireSSL="true" httpOnlyCookies="true" />
  
  <!-- Secure session state -->
  <sessionState cookieless="false" regenerateExpiredSessionId="true" 
                cookieTimeout="30" httpOnlyCookies="true" />
  
  <!-- Custom errors for production -->
  <customErrors mode="On" defaultRedirect="~/Error.aspx" />
  
  <!-- Remove server information -->
  <httpRuntime enableVersionHeader="false" />
</system.web>

<system.webServer>
  <!-- Security headers -->
  <httpProtocol>
    <customHeaders>
      <add name="X-Frame-Options" value="DENY" />
      <add name="X-Content-Type-Options" value="nosniff" />
      <add name="X-XSS-Protection" value="1; mode=block" />
    </customHeaders>
  </httpProtocol>
</system.webServer>
```

### 5. **File Permissions**
- Set appropriate file system permissions
- Ensure upload directories are secure
- Limit write access to necessary folders only

### 6. **SSL/HTTPS Configuration**
- Install SSL certificate
- Configure HTTPS redirects
- Update all internal links to use HTTPS

## 🚀 Deployment Steps

### 1. **Pre-Deployment**
- [ ] Update all default credentials
- [ ] Configure production database
- [ ] Update API keys and secrets
- [ ] Test in staging environment
- [ ] Run security scan

### 2. **Database Migration**
- [ ] Create production database
- [ ] Update connection strings
- [ ] Test database connectivity
- [ ] Verify admin user creation

### 3. **Application Deployment**
- [ ] Deploy to production server
- [ ] Configure IIS/web server
- [ ] Set up SSL certificate
- [ ] Configure security headers

### 4. **Post-Deployment**
- [ ] Test login functionality
- [ ] Verify all features work
- [ ] Check error logging
- [ ] Monitor performance

## 🔧 Configuration Management

### **Environment-Specific Settings**
Use `Web.Release.config` for production transformations:
```xml
<configuration xmlns:xdt="http://schemas.microsoft.com/XML-Document-Transform">
  <connectionStrings>
    <add name="MediEaseConnection" 
         connectionString="[Production Connection String]" 
         xdt:Transform="SetAttributes" xdt:Locator="Match(name)" />
  </connectionStrings>
  
  <appSettings>
    <add key="OpenRouterApiKey" value="[Production API Key]" 
         xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
  </appSettings>
</configuration>
```

## 📊 Monitoring & Maintenance

### **Health Checks**
- Database connectivity
- AI service availability
- File upload functionality
- User authentication

### **Regular Maintenance**
- Monitor error logs
- Update security patches
- Backup database regularly
- Review user access logs

## 🆘 Troubleshooting

### **Common Issues**
1. **Database Connection Errors**
   - Verify connection string
   - Check SQL Server service
   - Validate credentials

2. **AI Service Errors**
   - Verify API key validity
   - Check network connectivity
   - Monitor API usage limits

3. **Authentication Issues**
   - Check password hashing
   - Verify user roles
   - Review session configuration

## 📞 Support

For production deployment support:
- Review application logs in `App_Data/Logs/`
- Use the built-in diagnostics at `/Test.aspx`
- Check database connectivity and user permissions

---

**⚠️ Important**: Never deploy with default credentials or demo accounts enabled in production!
