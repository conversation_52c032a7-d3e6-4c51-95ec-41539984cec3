using System;
using System.Linq;
using System.Web.UI;
using System.Web.UI.WebControls;
using MediEase.DAL;
using MediEase.Utilities;

namespace MediEase.Pharmacist
{
    public partial class Dashboard : Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            // Check if user is pharmacist
            var currentUser = SecurityHelper.GetCurrentUser();
            if (currentUser == null || currentUser.Role != "Pharmacist")
            {
                Response.Redirect("~/Login.aspx");
                return;
            }

            if (!IsPostBack)
            {
                LoadDashboardData();
            }
        }

        private void LoadDashboardData()
        {
            try
            {
                using (var db = new MediEaseContext())
                {
                    // Load statistics
                    LoadStatistics(db);
                    
                    // Load pending prescriptions
                    LoadPendingPrescriptions(db);
                    
                    // Load orders to process
                    LoadOrdersToProcess(db);
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error loading pharmacist dashboard data");
                ShowErrorMessage("Error loading dashboard data. Please try again.");
            }
        }

        private void LoadStatistics(MediEaseContext db)
        {
            // Pending prescriptions
            var pendingPrescriptions = db.Prescriptions.Count(p => p.Status == "Pending");
            lblPendingPrescriptions.Text = pendingPrescriptions.ToString();

            // Orders to process
            var ordersToProcess = db.Orders.Count(o => o.Status == "Pending" || o.Status == "Processing");
            lblOrdersToProcess.Text = ordersToProcess.ToString();

            // Low stock items
            var lowStockItems = db.Medicines.Count(m => m.IsActive && m.StockQuantity <= m.MinimumStockLevel);
            lblLowStockItems.Text = lowStockItems.ToString();

            // Today's sales
            var today = DateTime.Today;
            var todaysSales = db.Orders
                .Where(o => o.OrderDate >= today && o.OrderDate < today.AddDays(1) && o.Status == "Delivered")
                .Sum(o => (decimal?)o.TotalAmount) ?? 0;
            lblTodaysSales.Text = todaysSales.ToString("N2");
        }

        private void LoadPendingPrescriptions(MediEaseContext db)
        {
            var pendingPrescriptions = db.Prescriptions
                .Where(p => p.Status == "Pending")
                .OrderBy(p => p.PrescriptionDate)
                .Take(10)
                .Select(p => new
                {
                    p.PrescriptionId,
                    p.PrescriptionNumber,
                    PatientName = p.Patient.FirstName + " " + p.Patient.LastName,
                    p.DoctorName,
                    p.PrescriptionDate
                })
                .ToList();

            gvPendingPrescriptions.DataSource = pendingPrescriptions;
            gvPendingPrescriptions.DataBind();
        }

        private void LoadOrdersToProcess(MediEaseContext db)
        {
            var ordersToProcess = db.Orders
                .Where(o => o.Status == "Pending" || o.Status == "Processing")
                .OrderBy(o => o.OrderDate)
                .Take(10)
                .Select(o => new
                {
                    o.OrderId,
                    o.OrderNumber,
                    CustomerName = o.Customer.FirstName + " " + o.Customer.LastName,
                    o.TotalAmount,
                    o.OrderDate
                })
                .ToList();

            gvOrdersToProcess.DataSource = ordersToProcess;
            gvOrdersToProcess.DataBind();
        }

        protected void btnRefresh_Click(object sender, EventArgs e)
        {
            LoadDashboardData();
            ShowSuccessMessage("Dashboard data refreshed successfully.");
        }

        protected void lnkVerifyPrescriptions_Click(object sender, EventArgs e)
        {
            Response.Redirect("~/Pharmacist/Prescriptions.aspx");
        }

        protected void lnkProcessOrders_Click(object sender, EventArgs e)
        {
            Response.Redirect("~/Pharmacist/Orders.aspx");
        }

        protected void lnkManageInventory_Click(object sender, EventArgs e)
        {
            Response.Redirect("~/Pharmacist/Inventory.aspx");
        }

        protected void lnkCustomerConsultation_Click(object sender, EventArgs e)
        {
            Response.Redirect("~/Pharmacist/Consultation.aspx");
        }

        protected void lnkVerify_Click(object sender, EventArgs e)
        {
            var linkButton = (LinkButton)sender;
            var prescriptionId = Convert.ToInt32(linkButton.CommandArgument);
            
            Response.Redirect($"~/Pharmacist/Prescriptions.aspx?id={prescriptionId}&action=verify");
        }

        protected void lnkProcess_Click(object sender, EventArgs e)
        {
            var linkButton = (LinkButton)sender;
            var orderId = Convert.ToInt32(linkButton.CommandArgument);
            
            Response.Redirect($"~/Pharmacist/Orders.aspx?id={orderId}&action=process");
        }

        private void ShowSuccessMessage(string message)
        {
            var master = Master as SiteMaster;
            master?.ShowSuccessMessage(message);
        }

        private void ShowErrorMessage(string message)
        {
            var master = Master as SiteMaster;
            master?.ShowErrorMessage(message);
        }
    }
}
