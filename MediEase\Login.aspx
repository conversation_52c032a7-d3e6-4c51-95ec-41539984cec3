<%@ Page Title="Login" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="Login.aspx.cs" Inherits="MediEase.Login" %>

<asp:Content ID="BodyContent" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-5 col-md-7">
                <div class="card shadow-lg border-0 mt-5">
                    <div class="card-header bg-primary text-white text-center py-4">
                        <h3 class="mb-0">
                            <i class="fas fa-sign-in-alt me-2"></i>Login to MediEase
                        </h3>
                        <p class="mb-0 mt-2 opacity-75">Access your pharmacy account</p>
                    </div>
                    <div class="card-body p-5">
                        <!-- Login Form -->
                        <div class="needs-validation" novalidate>
                            <!-- Email Field -->
                            <div class="mb-4">
                                <label for="txtEmail" class="form-label">
                                    <i class="fas fa-envelope me-1"></i>Email Address
                                </label>
                                <asp:TextBox ID="txtEmail" runat="server" 
                                    CssClass="form-control form-control-lg email-input" 
                                    TextMode="Email" 
                                    placeholder="Enter your email address"
                                    required="true" />
                                <asp:RequiredFieldValidator ID="rfvEmail" runat="server" 
                                    ControlToValidate="txtEmail" 
                                    ErrorMessage="Email is required" 
                                    CssClass="invalid-feedback" 
                                    Display="Dynamic" />
                                <asp:RegularExpressionValidator ID="revEmail" runat="server" 
                                    ControlToValidate="txtEmail" 
                                    ErrorMessage="Please enter a valid email address" 
                                    ValidationExpression="^[^\s@]+@[^\s@]+\.[^\s@]+$" 
                                    CssClass="invalid-feedback" 
                                    Display="Dynamic" />
                                <div class="invalid-feedback"></div>
                            </div>

                            <!-- Password Field -->
                            <div class="mb-4">
                                <label for="txtPassword" class="form-label">
                                    <i class="fas fa-lock me-1"></i>Password
                                </label>
                                <div class="input-group">
                                    <asp:TextBox ID="txtPassword" runat="server" 
                                        CssClass="form-control form-control-lg" 
                                        TextMode="Password" 
                                        placeholder="Enter your password"
                                        required="true" />
                                    <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                                <asp:RequiredFieldValidator ID="rfvPassword" runat="server" 
                                    ControlToValidate="txtPassword" 
                                    ErrorMessage="Password is required" 
                                    CssClass="invalid-feedback" 
                                    Display="Dynamic" />
                                <div class="invalid-feedback"></div>
                            </div>

                            <!-- Remember Me & Forgot Password -->
                            <div class="d-flex justify-content-between align-items-center mb-4">
                                <div class="form-check">
                                    <asp:CheckBox ID="chkRememberMe" runat="server" CssClass="form-check-input" />
                                    <label class="form-check-label" for="<%= chkRememberMe.ClientID %>">
                                        Remember me
                                    </label>
                                </div>
                                <a href="~/ForgotPassword.aspx" runat="server" class="text-decoration-none">
                                    Forgot Password?
                                </a>
                            </div>

                            <!-- Login Button -->
                            <div class="d-grid mb-4">
                                <asp:Button ID="btnLogin" runat="server" 
                                    CssClass="btn btn-primary btn-lg" 
                                    Text="Login" 
                                    OnClick="btnLogin_Click" />
                            </div>

                            <!-- Error Message -->
                            <asp:Panel ID="pnlError" runat="server" Visible="false" CssClass="alert alert-danger">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <asp:Literal ID="litErrorMessage" runat="server" />
                            </asp:Panel>

                            <!-- Success Message -->
                            <asp:Panel ID="pnlSuccess" runat="server" Visible="false" CssClass="alert alert-success">
                                <i class="fas fa-check-circle me-2"></i>
                                <asp:Literal ID="litSuccessMessage" runat="server" />
                            </asp:Panel>
                        </div>

                        <!-- Divider -->
                        <div class="text-center my-4">
                            <hr class="my-3">
                            <span class="text-muted bg-white px-3">Don't have an account?</span>
                        </div>

                        <!-- Register Link -->
                        <div class="text-center">
                            <a href="~/Register.aspx" runat="server" class="btn btn-outline-primary btn-lg">
                                <i class="fas fa-user-plus me-2"></i>Create New Account
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Demo Accounts Info -->
                <div class="card mt-4 border-info">
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>Demo Accounts
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-md-4">
                                <h6 class="text-primary">Admin</h6>
                                <small class="text-muted">
                                    <EMAIL><br>
                                    Admin@123
                                </small>
                            </div>
                            <div class="col-md-4">
                                <h6 class="text-success">Pharmacist</h6>
                                <small class="text-muted">
                                    <EMAIL><br>
                                    Pharmacist@123
                                </small>
                            </div>
                            <div class="col-md-4">
                                <h6 class="text-info">Customer</h6>
                                <small class="text-muted">
                                    <EMAIL><br>
                                    Customer@123
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</asp:Content>

<asp:Content ID="ScriptContent" ContentPlaceHolderID="ScriptContent" runat="server">
    <script>
        $(document).ready(function() {
            // Initialize login page
            initializeLoginPage();
            
            // Focus on email field
            $('#<%= txtEmail.ClientID %>').focus();
        });

        function initializeLoginPage() {
            // Password toggle functionality
            $('#togglePassword').click(function() {
                const passwordField = $('#<%= txtPassword.ClientID %>');
                const icon = $(this).find('i');
                
                if (passwordField.attr('type') === 'password') {
                    passwordField.attr('type', 'text');
                    icon.removeClass('fa-eye').addClass('fa-eye-slash');
                } else {
                    passwordField.attr('type', 'password');
                    icon.removeClass('fa-eye-slash').addClass('fa-eye');
                }
            });

            // Demo account quick login
            $('.demo-login').click(function(e) {
                e.preventDefault();
                const email = $(this).data('email');
                const password = $(this).data('password');
                
                $('#<%= txtEmail.ClientID %>').val(email);
                $('#<%= txtPassword.ClientID %>').val(password);
                
                // Trigger login
                $('#<%= btnLogin.ClientID %>').click();
            });

            // Form validation
            setupFormValidation();
            
            // Auto-hide alerts after 5 seconds
            setTimeout(function() {
                $('.alert').fadeOut();
            }, 5000);
        }

        function setupFormValidation() {
            const form = $('.needs-validation')[0];
            
            $('#<%= btnLogin.ClientID %>').click(function(e) {
                if (!form.checkValidity()) {
                    e.preventDefault();
                    e.stopPropagation();
                }
                $(form).addClass('was-validated');
            });

            // Real-time email validation
            $('#<%= txtEmail.ClientID %>').on('blur', function() {
                validateEmailField($(this));
            });

            // Real-time password validation
            $('#<%= txtPassword.ClientID %>').on('input', function() {
                validatePasswordField($(this));
            });
        }

        function validateEmailField(input) {
            const email = input.val();
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            
            if (email && !emailRegex.test(email)) {
                input.addClass('is-invalid');
                input.siblings('.invalid-feedback').text('Please enter a valid email address.');
            } else if (email) {
                input.removeClass('is-invalid').addClass('is-valid');
            }
        }

        function validatePasswordField(input) {
            const password = input.val();
            
            if (password && password.length < 6) {
                input.addClass('is-invalid');
                input.siblings('.invalid-feedback').text('Password must be at least 6 characters long.');
            } else if (password) {
                input.removeClass('is-invalid').addClass('is-valid');
            }
        }

        // Handle Enter key press
        $(document).keypress(function(e) {
            if (e.which === 13) { // Enter key
                $('#<%= btnLogin.ClientID %>').click();
            }
        });

        // Add demo account quick login buttons
        function addDemoLoginButtons() {
            const demoAccounts = [
                { role: 'Admin', email: '<EMAIL>', password: 'Admin@123', class: 'btn-primary' },
                { role: 'Pharmacist', email: '<EMAIL>', password: 'Pharmacist@123', class: 'btn-success' },
                { role: 'Customer', email: '<EMAIL>', password: 'Customer@123', class: 'btn-info' }
            ];

            const container = $('.card-body .row');
            demoAccounts.forEach(account => {
                const button = `
                    <div class="col-md-4 mb-2">
                        <button type="button" class="btn ${account.class} btn-sm w-100 demo-login" 
                                data-email="${account.email}" data-password="${account.password}">
                            Login as ${account.role}
                        </button>
                    </div>
                `;
                container.append(button);
            });
        }

        // Call after DOM is ready
        setTimeout(addDemoLoginButtons, 100);
    </script>
</asp:Content>
