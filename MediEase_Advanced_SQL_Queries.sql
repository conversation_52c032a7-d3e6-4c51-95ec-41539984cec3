-- =============================================
-- MediEase Advanced SQL Queries
-- Specialized queries for advanced features
-- =============================================

-- =============================================
-- 1. AI CHATBOT AND RECOMMENDATIONS
-- =============================================

-- Create Chat Session
INSERT INTO ChatSessions (UserId, SessionToken, StartDate, IsActive, UserAgent, IPAddress)
VALUES (@UserId, @SessionToken, GETDATE(), 1, @UserAgent, @IPAddress);

-- Add Chat Message
INSERT INTO ChatMessages (SessionId, MessageType, Message, Timestamp, AIModel, ResponseTime, TokensUsed)
VALUES (@SessionId, @MessageType, @Message, GETDATE(), @AIModel, @ResponseTime, @TokensUsed);

-- Get Chat History
SELECT MessageId, MessageType, Message, Timestamp, AIModel, ResponseTime
FROM ChatMessages 
WHERE SessionId = @SessionId 
ORDER BY Timestamp ASC;

-- End Chat Session
UPDATE ChatSessions 
SET EndDate = GETDATE(), IsActive = 0
WHERE SessionId = @SessionId;

-- Create AI Recommendation
INSERT INTO AIRecommendations (UserId, SessionId, RecommendationType, MedicineId, 
                              RecommendationText, ConfidenceScore, Reasoning, CreatedDate)
VALUES (@UserId, @SessionId, @RecommendationType, @MedicineId,
        @RecommendationText, @ConfidenceScore, @Reasoning, GETDATE());

-- Get Medicine Recommendations
SELECT ar.RecommendationId, ar.RecommendationType, ar.RecommendationText, ar.ConfidenceScore,
       ar.Reasoning, ar.CreatedDate, m.Name as MedicineName, m.Price, m.ImageUrl
FROM AIRecommendations ar
LEFT JOIN Medicines m ON ar.MedicineId = m.MedicineId
WHERE ar.UserId = @UserId
ORDER BY ar.CreatedDate DESC;

-- =============================================
-- 2. FAMILY PROFILES AND HEALTH MANAGEMENT
-- =============================================

-- Add Family Member
INSERT INTO FamilyProfiles (UserId, MemberName, Relationship, DateOfBirth, Gender,
                           BloodGroup, Allergies, MedicalConditions, EmergencyContact,
                           EmergencyPhone, IsActive, CreatedDate)
VALUES (@UserId, @MemberName, @Relationship, @DateOfBirth, @Gender,
        @BloodGroup, @Allergies, @MedicalConditions, @EmergencyContact,
        @EmergencyPhone, 1, GETDATE());

-- Get Family Members
SELECT FamilyProfileId, MemberName, Relationship, DateOfBirth, Gender, BloodGroup,
       Allergies, MedicalConditions, EmergencyContact, EmergencyPhone, CreatedDate
FROM FamilyProfiles 
WHERE UserId = @UserId AND IsActive = 1
ORDER BY CreatedDate ASC;

-- Update Family Member
UPDATE FamilyProfiles 
SET MemberName = @MemberName, Relationship = @Relationship, DateOfBirth = @DateOfBirth,
    Gender = @Gender, BloodGroup = @BloodGroup, Allergies = @Allergies,
    MedicalConditions = @MedicalConditions, EmergencyContact = @EmergencyContact,
    EmergencyPhone = @EmergencyPhone
WHERE FamilyProfileId = @FamilyProfileId AND UserId = @UserId;

-- Create Health Reminder
INSERT INTO HealthReminders (UserId, FamilyProfileId, Title, Description, ReminderType,
                            NextReminderDate, Frequency, FrequencyValue, EndDate,
                            IsActive, CreatedDate)
VALUES (@UserId, @FamilyProfileId, @Title, @Description, @ReminderType,
        @NextReminderDate, @Frequency, @FrequencyValue, @EndDate, 1, GETDATE());

-- Get Active Health Reminders
SELECT hr.ReminderId, hr.Title, hr.Description, hr.ReminderType, hr.NextReminderDate,
       hr.Frequency, hr.IsCompleted, fp.MemberName
FROM HealthReminders hr
LEFT JOIN FamilyProfiles fp ON hr.FamilyProfileId = fp.FamilyProfileId
WHERE hr.UserId = @UserId AND hr.IsActive = 1 AND hr.NextReminderDate <= GETDATE()
ORDER BY hr.NextReminderDate ASC;

-- Complete Health Reminder
UPDATE HealthReminders 
SET IsCompleted = 1, CompletedDate = GETDATE()
WHERE ReminderId = @ReminderId AND UserId = @UserId;

-- Snooze Health Reminder
UPDATE HealthReminders 
SET NextReminderDate = DATEADD(MINUTE, @SnoozeMinutes, GETDATE()),
    SnoozeCount = SnoozeCount + 1, LastSnoozedDate = GETDATE()
WHERE ReminderId = @ReminderId AND UserId = @UserId;

-- =============================================
-- 3. OFFERS AND PROMOTIONS
-- =============================================

-- Create Offer
INSERT INTO Offers (Code, Title, Description, OfferType, DiscountValue, MinOrderAmount,
                   MaxDiscountAmount, UsageLimit, UserUsageLimit, StartDate, EndDate,
                   IsActive, ApplicableCategories, ApplicableMedicines, CreatedDate, CreatedBy)
VALUES (@Code, @Title, @Description, @OfferType, @DiscountValue, @MinOrderAmount,
        @MaxDiscountAmount, @UsageLimit, @UserUsageLimit, @StartDate, @EndDate,
        1, @ApplicableCategories, @ApplicableMedicines, GETDATE(), @CreatedBy);

-- Get Active Offers
SELECT OfferId, Code, Title, Description, OfferType, DiscountValue, MinOrderAmount,
       MaxDiscountAmount, UsageLimit, UsageCount, UserUsageLimit, StartDate, EndDate
FROM Offers 
WHERE IsActive = 1 AND StartDate <= GETDATE() AND EndDate >= GETDATE()
ORDER BY CreatedDate DESC;

-- Validate Offer Code
SELECT OfferId, Code, Title, OfferType, DiscountValue, MinOrderAmount, MaxDiscountAmount,
       UsageLimit, UsageCount, UserUsageLimit, ApplicableCategories, ApplicableMedicines
FROM Offers 
WHERE Code = @Code AND IsActive = 1 
  AND StartDate <= GETDATE() AND EndDate >= GETDATE()
  AND (UsageLimit IS NULL OR UsageCount < UsageLimit);

-- Check User Offer Usage
SELECT COUNT(*) as UsageCount
FROM UserOfferUsage 
WHERE UserId = @UserId AND OfferId = @OfferId;

-- Apply Offer
INSERT INTO UserOfferUsage (UserId, OfferId, OrderId, DiscountAmount, UsedDate)
VALUES (@UserId, @OfferId, @OrderId, @DiscountAmount, GETDATE());

UPDATE Offers 
SET UsageCount = UsageCount + 1
WHERE OfferId = @OfferId;

-- =============================================
-- 4. PRESCRIPTION PROCESSING
-- =============================================

-- Add Prescription Items
INSERT INTO PrescriptionItems (PrescriptionId, MedicineId, MedicineName, Dosage,
                              Frequency, Duration, Quantity, Instructions)
VALUES (@PrescriptionId, @MedicineId, @MedicineName, @Dosage,
        @Frequency, @Duration, @Quantity, @Instructions);

-- Get Prescription Items
SELECT pi.PrescriptionItemId, pi.MedicineId, pi.MedicineName, pi.Dosage, pi.Frequency,
       pi.Duration, pi.Quantity, pi.Instructions, pi.IsDispensed, pi.DispensedDate,
       m.Name as ActualMedicineName, m.Price, m.StockQuantity
FROM PrescriptionItems pi
LEFT JOIN Medicines m ON pi.MedicineId = m.MedicineId
WHERE pi.PrescriptionId = @PrescriptionId;

-- Mark Prescription Item as Dispensed
UPDATE PrescriptionItems 
SET IsDispensed = 1, DispensedDate = GETDATE(), DispensedBy = @DispensedBy
WHERE PrescriptionItemId = @PrescriptionItemId;

-- AI Process Prescription
UPDATE Prescriptions 
SET AIProcessed = 1, AIExtractedText = @ExtractedText, AIConfidenceScore = @ConfidenceScore,
    AIProcessingDate = GETDATE()
WHERE PrescriptionId = @PrescriptionId;

-- =============================================
-- 5. ADVANCED INVENTORY QUERIES
-- =============================================

-- Medicines Expiring Soon
SELECT MedicineId, Name, ExpiryDate, StockQuantity, 
       DATEDIFF(DAY, GETDATE(), ExpiryDate) as DaysToExpiry
FROM Medicines 
WHERE ExpiryDate IS NOT NULL 
  AND ExpiryDate <= DATEADD(DAY, @DaysThreshold, GETDATE())
  AND IsActive = 1
ORDER BY ExpiryDate ASC;

-- Stock Valuation Report
SELECT m.MedicineId, m.Name, m.StockQuantity, m.CostPrice, m.Price,
       (m.StockQuantity * m.CostPrice) as StockValue,
       (m.StockQuantity * m.Price) as PotentialRevenue,
       c.Name as CategoryName
FROM Medicines m
INNER JOIN Categories c ON m.CategoryId = c.CategoryId
WHERE m.IsActive = 1 AND m.StockQuantity > 0
ORDER BY StockValue DESC;

-- Fast Moving Items
SELECT m.MedicineId, m.Name, m.StockQuantity,
       SUM(oi.Quantity) as TotalSold,
       COUNT(DISTINCT o.OrderId) as OrderCount,
       AVG(oi.Quantity) as AvgQuantityPerOrder
FROM Medicines m
INNER JOIN OrderItems oi ON m.MedicineId = oi.MedicineId
INNER JOIN Orders o ON oi.OrderId = o.OrderId
WHERE o.OrderDate >= DATEADD(DAY, -30, GETDATE())
  AND o.Status NOT IN ('Cancelled')
GROUP BY m.MedicineId, m.Name, m.StockQuantity
HAVING SUM(oi.Quantity) > 10
ORDER BY TotalSold DESC;

-- Slow Moving Items
SELECT m.MedicineId, m.Name, m.StockQuantity, m.CreatedDate,
       ISNULL(SUM(oi.Quantity), 0) as TotalSold,
       DATEDIFF(DAY, m.CreatedDate, GETDATE()) as DaysInInventory
FROM Medicines m
LEFT JOIN OrderItems oi ON m.MedicineId = oi.MedicineId
LEFT JOIN Orders o ON oi.OrderId = o.OrderId AND o.Status NOT IN ('Cancelled')
WHERE m.IsActive = 1 AND m.StockQuantity > 0
GROUP BY m.MedicineId, m.Name, m.StockQuantity, m.CreatedDate
HAVING ISNULL(SUM(oi.Quantity), 0) < 5 AND DATEDIFF(DAY, m.CreatedDate, GETDATE()) > 30
ORDER BY TotalSold ASC, DaysInInventory DESC;

-- =============================================
-- 6. CUSTOMER ANALYTICS
-- =============================================

-- Customer Lifetime Value
SELECT u.UserId, u.FirstName, u.LastName, u.Email, u.CreatedDate,
       COUNT(o.OrderId) as TotalOrders,
       SUM(o.TotalAmount) as LifetimeValue,
       AVG(o.TotalAmount) as AverageOrderValue,
       MIN(o.OrderDate) as FirstOrderDate,
       MAX(o.OrderDate) as LastOrderDate,
       DATEDIFF(DAY, MIN(o.OrderDate), MAX(o.OrderDate)) as CustomerLifespanDays,
       u.LoyaltyPoints
FROM Users u
LEFT JOIN Orders o ON u.UserId = o.CustomerId AND o.Status NOT IN ('Cancelled')
WHERE u.Role = 'Customer'
GROUP BY u.UserId, u.FirstName, u.LastName, u.Email, u.CreatedDate, u.LoyaltyPoints
HAVING COUNT(o.OrderId) > 0
ORDER BY LifetimeValue DESC;

-- Customer Segmentation
SELECT 
    CASE 
        WHEN TotalSpent >= 1000 THEN 'VIP'
        WHEN TotalSpent >= 500 THEN 'Premium'
        WHEN TotalSpent >= 100 THEN 'Regular'
        ELSE 'New'
    END as CustomerSegment,
    COUNT(*) as CustomerCount,
    AVG(TotalSpent) as AvgSpent,
    AVG(TotalOrders) as AvgOrders
FROM (
    SELECT u.UserId, 
           ISNULL(SUM(o.TotalAmount), 0) as TotalSpent,
           COUNT(o.OrderId) as TotalOrders
    FROM Users u
    LEFT JOIN Orders o ON u.UserId = o.CustomerId AND o.Status NOT IN ('Cancelled')
    WHERE u.Role = 'Customer'
    GROUP BY u.UserId
) CustomerStats
GROUP BY 
    CASE 
        WHEN TotalSpent >= 1000 THEN 'VIP'
        WHEN TotalSpent >= 500 THEN 'Premium'
        WHEN TotalSpent >= 100 THEN 'Regular'
        ELSE 'New'
    END
ORDER BY AvgSpent DESC;

-- Customer Purchase Patterns
SELECT u.UserId, u.FirstName, u.LastName,
       COUNT(DISTINCT c.CategoryId) as CategoriesPurchased,
       STRING_AGG(c.Name, ', ') as PreferredCategories,
       COUNT(DISTINCT CASE WHEN m.PrescriptionRequired = 1 THEN o.OrderId END) as PrescriptionOrders,
       COUNT(DISTINCT CASE WHEN m.PrescriptionRequired = 0 THEN o.OrderId END) as OTCOrders
FROM Users u
INNER JOIN Orders o ON u.UserId = o.CustomerId
INNER JOIN OrderItems oi ON o.OrderId = oi.OrderId
INNER JOIN Medicines m ON oi.MedicineId = m.MedicineId
INNER JOIN Categories c ON m.CategoryId = c.CategoryId
WHERE o.Status NOT IN ('Cancelled') AND u.Role = 'Customer'
GROUP BY u.UserId, u.FirstName, u.LastName
ORDER BY CategoriesPurchased DESC;

-- =============================================
-- 7. FINANCIAL REPORTS
-- =============================================

-- Revenue by Category
SELECT c.CategoryId, c.Name as CategoryName,
       COUNT(DISTINCT o.OrderId) as OrderCount,
       SUM(oi.Quantity) as TotalQuantitySold,
       SUM(oi.TotalPrice) as TotalRevenue,
       AVG(oi.UnitPrice) as AveragePrice
FROM Categories c
INNER JOIN Medicines m ON c.CategoryId = m.CategoryId
INNER JOIN OrderItems oi ON m.MedicineId = oi.MedicineId
INNER JOIN Orders o ON oi.OrderId = o.OrderId
WHERE o.Status NOT IN ('Cancelled')
  AND o.OrderDate >= @StartDate AND o.OrderDate <= @EndDate
GROUP BY c.CategoryId, c.Name
ORDER BY TotalRevenue DESC;

-- Profit Analysis
SELECT m.MedicineId, m.Name, 
       SUM(oi.Quantity) as TotalSold,
       SUM(oi.TotalPrice) as TotalRevenue,
       SUM(oi.Quantity * m.CostPrice) as TotalCost,
       SUM(oi.TotalPrice) - SUM(oi.Quantity * m.CostPrice) as TotalProfit,
       ((SUM(oi.TotalPrice) - SUM(oi.Quantity * m.CostPrice)) / SUM(oi.TotalPrice)) * 100 as ProfitMargin
FROM Medicines m
INNER JOIN OrderItems oi ON m.MedicineId = oi.MedicineId
INNER JOIN Orders o ON oi.OrderId = o.OrderId
WHERE o.Status NOT IN ('Cancelled')
  AND o.OrderDate >= @StartDate AND o.OrderDate <= @EndDate
  AND m.CostPrice IS NOT NULL
GROUP BY m.MedicineId, m.Name
HAVING SUM(oi.Quantity) > 0
ORDER BY TotalProfit DESC;

-- Payment Method Analysis
SELECT PaymentMethod,
       COUNT(*) as OrderCount,
       SUM(TotalAmount) as TotalRevenue,
       AVG(TotalAmount) as AverageOrderValue,
       (COUNT(*) * 100.0 / (SELECT COUNT(*) FROM Orders WHERE Status NOT IN ('Cancelled'))) as Percentage
FROM Orders 
WHERE Status NOT IN ('Cancelled')
  AND OrderDate >= @StartDate AND OrderDate <= @EndDate
GROUP BY PaymentMethod
ORDER BY TotalRevenue DESC;
