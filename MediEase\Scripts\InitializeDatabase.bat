@echo off
echo =============================================
echo MediEase Database Initialization Script
echo =============================================
echo.

echo Checking for SQL Server LocalDB...
sqlcmd -S "(LocalDB)\MSSQLLocalDB" -Q "SELECT @@VERSION" >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: SQL Server LocalDB is not installed or not running.
    echo Please install SQL Server LocalDB and try again.
    pause
    exit /b 1
)

echo LocalDB found. Proceeding with database creation...
echo.

echo Creating MediEase database...
sqlcmd -S "(LocalDB)\MSSQLLocalDB" -i "CreateLocalDatabase.sql"

if %errorlevel% equ 0 (
    echo.
    echo =============================================
    echo SUCCESS: MediEase database created successfully!
    echo =============================================
    echo.
    echo Database Details:
    echo - Server: (LocalDB)\MSSQLLocalDB
    echo - Database: MediEase
    echo - Location: App_Data\MediEase.mdf
    echo.
    echo Default Admin Account:
    echo - Email: <EMAIL>
    echo - Password: password (CHANGE THIS!)
    echo.
    echo Next Steps:
    echo 1. Change the default admin password
    echo 2. Update Web.config if needed
    echo 3. Build and run the application
    echo.
) else (
    echo.
    echo ERROR: Database creation failed.
    echo Please check the error messages above.
    echo.
)

pause
