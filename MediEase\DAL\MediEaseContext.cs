using System;
using System.Data.Entity;
using System.Data.Entity.ModelConfiguration.Conventions;
using MediEase.Models;

namespace MediEase.DAL
{
    public class MediEaseContext : DbContext
    {
        public MediEaseContext() : base("MediEaseConnection")
        {
            Database.SetInitializer(new MediEaseInitializer());
        }

        // DbSets for all entities
        public DbSet<User> Users { get; set; }
        public DbSet<Medicine> Medicines { get; set; }
        public DbSet<Order> Orders { get; set; }
        public DbSet<OrderItem> OrderItems { get; set; }
        public DbSet<Prescription> Prescriptions { get; set; }
        public DbSet<PrescriptionItem> PrescriptionItems { get; set; }
        public DbSet<Category> Categories { get; set; }
        public DbSet<Brand> Brands { get; set; }
        public DbSet<Review> Reviews { get; set; }
        public DbSet<CartItem> CartItems { get; set; }
        public DbSet<Notification> Notifications { get; set; }
        public DbSet<AuditLog> AuditLogs { get; set; }
        public DbSet<ChatMessage> ChatMessages { get; set; }
        public DbSet<Discount> Discounts { get; set; }
        public DbSet<LoyaltyTransaction> LoyaltyTransactions { get; set; }
        public DbSet<ErrorLog> ErrorLogs { get; set; }
        public DbSet<ContactMessage> ContactMessages { get; set; }
        public DbSet<PasswordReset> PasswordResets { get; set; }
        public DbSet<FamilyMember> FamilyMembers { get; set; }
        public DbSet<LoyaltyPoint> LoyaltyPoints { get; set; }
        public DbSet<PriceComparison> PriceComparisons { get; set; }
        public DbSet<AutoRefill> AutoRefills { get; set; }
        public DbSet<HealthReminder> HealthReminders { get; set; }
        public DbSet<Feedback> Feedback { get; set; }
        public DbSet<AccessibilitySettings> AccessibilitySettings { get; set; }
        public DbSet<InternalMessage> InternalMessages { get; set; }
        public DbSet<BulkUploadHistory> BulkUploadHistory { get; set; }
        public DbSet<ReportSchedule> ReportSchedules { get; set; }
        public DbSet<SystemConfiguration> SystemConfigurations { get; set; }
        public DbSet<Payment> Payments { get; set; }
        public DbSet<Refund> Refunds { get; set; }
        public DbSet<BackupHistory> BackupHistory { get; set; }

        protected override void OnModelCreating(DbModelBuilder modelBuilder)
        {
            // Remove pluralizing table name convention
            modelBuilder.Conventions.Remove<PluralizingTableNameConvention>();

            // Configure User entity
            modelBuilder.Entity<User>()
                .HasIndex(u => u.Email)
                .IsUnique();

            // Configure Medicine entity
            modelBuilder.Entity<Medicine>()
                .Property(m => m.Price)
                .HasPrecision(10, 2);

            modelBuilder.Entity<Medicine>()
                .Property(m => m.DiscountAmount)
                .HasPrecision(10, 2);

            modelBuilder.Entity<Medicine>()
                .Property(m => m.DiscountPercentage)
                .HasPrecision(5, 2);

            // Configure Order entity
            modelBuilder.Entity<Order>()
                .Property(o => o.Subtotal)
                .HasPrecision(10, 2);

            modelBuilder.Entity<Order>()
                .Property(o => o.TaxAmount)
                .HasPrecision(10, 2);

            modelBuilder.Entity<Order>()
                .Property(o => o.ShippingCost)
                .HasPrecision(10, 2);

            modelBuilder.Entity<Order>()
                .Property(o => o.DiscountAmount)
                .HasPrecision(10, 2);

            modelBuilder.Entity<Order>()
                .Property(o => o.TotalAmount)
                .HasPrecision(10, 2);

            // Configure OrderItem entity
            modelBuilder.Entity<OrderItem>()
                .Property(oi => oi.UnitPrice)
                .HasPrecision(10, 2);

            modelBuilder.Entity<OrderItem>()
                .Property(oi => oi.DiscountAmount)
                .HasPrecision(10, 2);

            modelBuilder.Entity<OrderItem>()
                .Property(oi => oi.TotalPrice)
                .HasPrecision(10, 2);

            // Configure Prescription entity
            modelBuilder.Entity<Prescription>()
                .Property(p => p.TotalAmount)
                .HasPrecision(10, 2);

            modelBuilder.Entity<Prescription>()
                .Property(p => p.InsuranceCoverage)
                .HasPrecision(10, 2);

            modelBuilder.Entity<Prescription>()
                .Property(p => p.PatientCopay)
                .HasPrecision(10, 2);

            // Configure PrescriptionItem entity
            modelBuilder.Entity<PrescriptionItem>()
                .Property(pi => pi.UnitPrice)
                .HasPrecision(10, 2);

            modelBuilder.Entity<PrescriptionItem>()
                .Property(pi => pi.TotalPrice)
                .HasPrecision(10, 2);

            // Configure relationships
            modelBuilder.Entity<Order>()
                .HasRequired(o => o.Customer)
                .WithMany()
                .HasForeignKey(o => o.CustomerId)
                .WillCascadeOnDelete(false);

            modelBuilder.Entity<Order>()
                .HasOptional(o => o.Prescription)
                .WithMany(p => p.Orders)
                .HasForeignKey(o => o.PrescriptionId)
                .WillCascadeOnDelete(false);

            modelBuilder.Entity<OrderItem>()
                .HasRequired(oi => oi.Order)
                .WithMany(o => o.OrderItems)
                .HasForeignKey(oi => oi.OrderId)
                .WillCascadeOnDelete(true);

            modelBuilder.Entity<OrderItem>()
                .HasRequired(oi => oi.Medicine)
                .WithMany()
                .HasForeignKey(oi => oi.MedicineId)
                .WillCascadeOnDelete(false);

            modelBuilder.Entity<Prescription>()
                .HasRequired(p => p.Patient)
                .WithMany()
                .HasForeignKey(p => p.PatientId)
                .WillCascadeOnDelete(false);

            modelBuilder.Entity<PrescriptionItem>()
                .HasRequired(pi => pi.Prescription)
                .WithMany(p => p.PrescriptionItems)
                .HasForeignKey(pi => pi.PrescriptionId)
                .WillCascadeOnDelete(true);

            modelBuilder.Entity<PrescriptionItem>()
                .HasOptional(pi => pi.Medicine)
                .WithMany()
                .HasForeignKey(pi => pi.MedicineId)
                .WillCascadeOnDelete(false);

            // Self-referencing relationship for Prescription
            modelBuilder.Entity<Prescription>()
                .HasOptional(p => p.OriginalPrescription)
                .WithMany()
                .HasForeignKey(p => p.OriginalPrescriptionId)
                .WillCascadeOnDelete(false);

            base.OnModelCreating(modelBuilder);
        }

        public override int SaveChanges()
        {
            // Add audit trail functionality
            var entries = ChangeTracker.Entries();
            foreach (var entry in entries)
            {
                if (entry.Entity is IAuditable auditableEntity)
                {
                    switch (entry.State)
                    {
                        case EntityState.Added:
                            auditableEntity.CreatedDate = DateTime.Now;
                            break;
                        case EntityState.Modified:
                            auditableEntity.ModifiedDate = DateTime.Now;
                            break;
                    }
                }
            }

            return base.SaveChanges();
        }
    }

    public interface IAuditable
    {
        DateTime CreatedDate { get; set; }
        DateTime? ModifiedDate { get; set; }
        int? CreatedBy { get; set; }
        int? ModifiedBy { get; set; }
    }

    public class MediEaseInitializer : DropCreateDatabaseIfModelChanges<MediEaseContext>
    {
        protected override void Seed(MediEaseContext context)
        {
            // Seed initial data
            SeedUsers(context);
            SeedCategories(context);
            SeedBrands(context);
            SeedMedicines(context);
            
            context.SaveChanges();
        }

        private void SeedUsers(MediEaseContext context)
        {
            var users = new[]
            {
                new User
                {
                    FirstName = "Admin",
                    LastName = "User",
                    Email = "<EMAIL>",
                    PasswordHash = BCrypt.Net.BCrypt.HashPassword("Admin@123"),
                    PhoneNumber = "+1234567890",
                    Role = "Admin",
                    IsActive = true,
                    IsEmailVerified = true,
                    Address = "123 Admin Street",
                    City = "Admin City",
                    PostalCode = "12345",
                    Country = "USA"
                },
                new User
                {
                    FirstName = "John",
                    LastName = "Pharmacist",
                    Email = "<EMAIL>",
                    PasswordHash = BCrypt.Net.BCrypt.HashPassword("Pharmacist@123"),
                    PhoneNumber = "+1234567891",
                    Role = "Pharmacist",
                    IsActive = true,
                    IsEmailVerified = true,
                    Address = "456 Pharmacy Lane",
                    City = "Pharmacy City",
                    PostalCode = "12346",
                    Country = "USA"
                },
                new User
                {
                    FirstName = "Jane",
                    LastName = "Customer",
                    Email = "<EMAIL>",
                    PasswordHash = BCrypt.Net.BCrypt.HashPassword("Customer@123"),
                    PhoneNumber = "+1234567892",
                    Role = "Customer",
                    IsActive = true,
                    IsEmailVerified = true,
                    Address = "789 Customer Road",
                    City = "Customer City",
                    PostalCode = "12347",
                    Country = "USA",
                    LoyaltyPoints = 100
                }
            };

            context.Users.AddRange(users);
        }

        private void SeedCategories(MediEaseContext context)
        {
            var categories = new[]
            {
                new Category { Name = "Pain Relief", Description = "Medications for pain management" },
                new Category { Name = "Antibiotics", Description = "Antimicrobial medications" },
                new Category { Name = "Vitamins", Description = "Vitamin supplements" },
                new Category { Name = "Cold & Flu", Description = "Medications for cold and flu symptoms" },
                new Category { Name = "Digestive Health", Description = "Medications for digestive issues" },
                new Category { Name = "Heart Health", Description = "Cardiovascular medications" },
                new Category { Name = "Diabetes Care", Description = "Diabetes management medications" },
                new Category { Name = "Skin Care", Description = "Topical medications and treatments" }
            };

            context.Categories.AddRange(categories);
        }

        private void SeedBrands(MediEaseContext context)
        {
            var brands = new[]
            {
                new Brand { Name = "Pfizer", Description = "Leading pharmaceutical company" },
                new Brand { Name = "Johnson & Johnson", Description = "Healthcare and pharmaceutical products" },
                new Brand { Name = "Novartis", Description = "Swiss multinational pharmaceutical company" },
                new Brand { Name = "Roche", Description = "Swiss healthcare company" },
                new Brand { Name = "Merck", Description = "American pharmaceutical company" },
                new Brand { Name = "GSK", Description = "British pharmaceutical company" },
                new Brand { Name = "Sanofi", Description = "French pharmaceutical company" },
                new Brand { Name = "AbbVie", Description = "American pharmaceutical company" }
            };

            context.Brands.AddRange(brands);
        }

        private void SeedMedicines(MediEaseContext context)
        {
            var medicines = new[]
            {
                new Medicine
                {
                    Name = "Paracetamol 500mg",
                    GenericName = "Acetaminophen",
                    Brand = "Pfizer",
                    Category = "Pain Relief",
                    DosageForm = "Tablet",
                    Strength = "500mg",
                    PackSize = 20,
                    Unit = "tablets",
                    Price = 5.99m,
                    StockQuantity = 100,
                    MinimumStockLevel = 10,
                    Manufacturer = "Pfizer Inc.",
                    Description = "Effective pain relief and fever reducer",
                    Indications = "Pain relief, fever reduction",
                    DosageInstructions = "1-2 tablets every 4-6 hours as needed",
                    PrescriptionRequired = false,
                    IsActive = true,
                    Barcode = "1234567890123",
                    SKU = "PAR500-20"
                },
                new Medicine
                {
                    Name = "Amoxicillin 250mg",
                    GenericName = "Amoxicillin",
                    Brand = "GSK",
                    Category = "Antibiotics",
                    DosageForm = "Capsule",
                    Strength = "250mg",
                    PackSize = 21,
                    Unit = "capsules",
                    Price = 12.99m,
                    StockQuantity = 50,
                    MinimumStockLevel = 5,
                    Manufacturer = "GlaxoSmithKline",
                    Description = "Broad-spectrum antibiotic",
                    Indications = "Bacterial infections",
                    DosageInstructions = "1 capsule three times daily",
                    PrescriptionRequired = true,
                    IsActive = true,
                    Barcode = "1234567890124",
                    SKU = "AMX250-21"
                }
            };

            context.Medicines.AddRange(medicines);
        }
    }
}
