<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="..\packages\EntityFramework.6.5.1\build\EntityFramework.props" Condition="Exists('..\packages\EntityFramework.6.5.1\build\EntityFramework.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>
    </ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{8B5C2B4D-6F1C-4B5A-8E3C-9D2E1F4A5B6C}</ProjectGuid>
    <ProjectTypeGuids>{349c5851-65df-11da-9384-00065b846f21};{fae04ec0-301f-11d3-bf4b-00c04f79efbc}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>MediEase</RootNamespace>
    <AssemblyName>MediEase</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <MvcBuildViews>false</MvcBuildViews>
    <UseIISExpress>true</UseIISExpress>
    <Use64BitIISExpress />
    <IISExpressSSLPort>44300</IISExpressSSLPort>
    <IISExpressAnonymousAuthentication />
    <IISExpressWindowsAuthentication />
    <IISExpressUseClassicPipelineMode />
    <UseGlobalApplicationHostFile />
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="EntityFramework, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.5.1\lib\net45\EntityFramework.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework.SqlServer, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.5.1\lib\net45\EntityFramework.SqlServer.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.EnterpriseServices" />
    <Reference Include="System.Web.DynamicData" />
    <Reference Include="System.Web.Entity" />
    <Reference Include="System.Web.ApplicationServices" />
    <Reference Include="Microsoft.Web.Infrastructure, Version=1.0.0.0, Culture=neutral, PublicKeyToken=31BF3856AD364E35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.Web.Infrastructure.1.0.0.0\lib\net40\Microsoft.Web.Infrastructure.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="BCrypt.Net-Next, Version=4.0.3.0, Culture=neutral, PublicKeyToken=1e11be04b6288443, processorArchitecture=MSIL">
      <HintPath>..\packages\BCrypt.Net-Next.4.0.3\lib\net48\BCrypt.Net-Next.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Web.config" />
    <Content Include="Admin\Dashboard.aspx" />
    <Content Include="Cart.aspx" />
    <Content Include="Checkout.aspx" />
    <Content Include="Controls\AIChatbot.ascx" />
    <Content Include="Customer\AIRecommendations.aspx" />
    <Content Include="Customer\Dashboard.aspx" />
    <Content Include="Customer\Profile.aspx" />
    <Content Include="Customer\UploadPrescription.aspx" />
    <Content Include="Customer\PriceComparison.aspx" />
    <Content Include="Customer\LoyaltyProgram.aspx" />
    <Content Include="Customer\OrderTracking.aspx" />
    <Content Include="Default.aspx" />
    <Content Include="GuestSearch.aspx" />
    <Content Include="Login.aspx" />
    <Content Include="OrderConfirmation.aspx" />
    <Content Include="Prescription.aspx" />
    <Content Include="Register.aspx" />
    <Content Include="Medicines.aspx" />
    <Content Include="About.aspx" />
    <Content Include="Contact.aspx" />
    <Content Include="FAQ.aspx" />
    <Content Include="ForgotPassword.aspx" />
    <Content Include="ResetPassword.aspx" />
    <Content Include="Pharmacist\Dashboard.aspx" />
    <Content Include="Pharmacist\PrescriptionValidation.aspx" />
    <Content Include="Pharmacist\OrderManagement.aspx" />
    <Content Include="Pharmacist\InventoryManagement.aspx" />
    <Content Include="Admin\UserManagement.aspx" />
    <Content Include="Customer\FamilyProfiles.aspx" />
    <Content Include="Customer\AccessibilitySettings.aspx" />
    <Content Include="Customer\AutoRefill.aspx" />
    <Content Include="Customer\HealthReminders.aspx" />
    <Content Include="Customer\FeedbackReviews.aspx" />
    <Content Include="Pharmacist\InvoiceGeneration.aspx" />
    <Content Include="Pharmacist\Reports.aspx" />
    <Content Include="Pharmacist\PriceManagement.aspx" />
    <Content Include="Admin\MedicineManagement.aspx" />
    <Content Include="Admin\BulkUpload.aspx" />
    <Content Include="Admin\SystemConfiguration.aspx" />
    <Content Include="Admin\BackupRestore.aspx" />
    <Content Include="Admin\ChatbotManagement.aspx" />
    <Content Include="Shared\InternalMessaging.aspx" />
    <Content Include="Site.Master" />
    <Content Include="Global.asax" />
    <Content Include="Content\Site.css" />
    <Content Include="Scripts\Site.js" />
    <Content Include="Handlers\ChatHandler.ashx" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Admin\Dashboard.aspx.cs">
      <DependentUpon>Dashboard.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Admin\Dashboard.aspx.designer.cs">
      <DependentUpon>Dashboard.aspx</DependentUpon>
    </Compile>

    <Compile Include="Controls\AIChatbot.ascx.cs">
      <DependentUpon>Controls\AIChatbot.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Controls\AIChatbot.ascx.designer.cs">
      <DependentUpon>Controls\AIChatbot.ascx</DependentUpon>
    </Compile>
    <Compile Include="Customer\AIRecommendations.aspx.cs">
      <DependentUpon>Customer\AIRecommendations.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Customer\AIRecommendations.aspx.designer.cs">
      <DependentUpon>Customer\AIRecommendations.aspx</DependentUpon>
    </Compile>
    <Compile Include="Cart.aspx.cs">
      <DependentUpon>Cart.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Cart.aspx.designer.cs">
      <DependentUpon>Cart.aspx</DependentUpon>
    </Compile>
    <Compile Include="Customer\Profile.aspx.cs">
      <DependentUpon>Customer\Profile.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Customer\Profile.aspx.designer.cs">
      <DependentUpon>Customer\Profile.aspx</DependentUpon>
    </Compile>
    <Compile Include="Customer\UploadPrescription.aspx.cs">
      <DependentUpon>Customer\UploadPrescription.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Customer\UploadPrescription.aspx.designer.cs">
      <DependentUpon>Customer\UploadPrescription.aspx</DependentUpon>
    </Compile>
    <Compile Include="Customer\PriceComparison.aspx.cs">
      <DependentUpon>Customer\PriceComparison.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Customer\PriceComparison.aspx.designer.cs">
      <DependentUpon>Customer\PriceComparison.aspx</DependentUpon>
    </Compile>
    <Compile Include="Customer\LoyaltyProgram.aspx.cs">
      <DependentUpon>Customer\LoyaltyProgram.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Customer\LoyaltyProgram.aspx.designer.cs">
      <DependentUpon>Customer\LoyaltyProgram.aspx</DependentUpon>
    </Compile>
    <Compile Include="Customer\OrderTracking.aspx.cs">
      <DependentUpon>Customer\OrderTracking.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Customer\OrderTracking.aspx.designer.cs">
      <DependentUpon>Customer\OrderTracking.aspx</DependentUpon>
    </Compile>
    <Compile Include="Checkout.aspx.cs">
      <DependentUpon>Checkout.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Checkout.aspx.designer.cs">
      <DependentUpon>Checkout.aspx</DependentUpon>
    </Compile>

    <Compile Include="Pharmacist\Dashboard.aspx.cs">
      <DependentUpon>Dashboard.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pharmacist\Dashboard.aspx.designer.cs">
      <DependentUpon>Dashboard.aspx</DependentUpon>
    </Compile>
    <Compile Include="Pharmacist\PrescriptionValidation.aspx.cs">
      <DependentUpon>PrescriptionValidation.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pharmacist\PrescriptionValidation.aspx.designer.cs">
      <DependentUpon>PrescriptionValidation.aspx</DependentUpon>
    </Compile>
    <Compile Include="Pharmacist\OrderManagement.aspx.cs">
      <DependentUpon>OrderManagement.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pharmacist\OrderManagement.aspx.designer.cs">
      <DependentUpon>OrderManagement.aspx</DependentUpon>
    </Compile>
    <Compile Include="Pharmacist\InventoryManagement.aspx.cs">
      <DependentUpon>InventoryManagement.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pharmacist\InventoryManagement.aspx.designer.cs">
      <DependentUpon>InventoryManagement.aspx</DependentUpon>
    </Compile>
    <Compile Include="Admin\UserManagement.aspx.cs">
      <DependentUpon>UserManagement.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Admin\UserManagement.aspx.designer.cs">
      <DependentUpon>UserManagement.aspx</DependentUpon>
    </Compile>
    <Compile Include="Customer\FamilyProfiles.aspx.cs">
      <DependentUpon>FamilyProfiles.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Customer\FamilyProfiles.aspx.designer.cs">
      <DependentUpon>FamilyProfiles.aspx</DependentUpon>
    </Compile>
    <Compile Include="Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Default.aspx.designer.cs">
      <DependentUpon>Default.aspx</DependentUpon>
    </Compile>
    <Compile Include="GuestSearch.aspx.cs">
      <DependentUpon>GuestSearch.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="GuestSearch.aspx.designer.cs">
      <DependentUpon>GuestSearch.aspx</DependentUpon>
    </Compile>
    <Compile Include="Login.aspx.cs">
      <DependentUpon>Login.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Login.aspx.designer.cs">
      <DependentUpon>Login.aspx</DependentUpon>
    </Compile>

    <Compile Include="OrderConfirmation.aspx.cs">
      <DependentUpon>OrderConfirmation.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="OrderConfirmation.aspx.designer.cs">
      <DependentUpon>OrderConfirmation.aspx</DependentUpon>
    </Compile>
    <Compile Include="Register.aspx.cs">
      <DependentUpon>Register.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Prescription.aspx.cs">
      <DependentUpon>Prescription.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Prescription.aspx.designer.cs">
      <DependentUpon>Prescription.aspx</DependentUpon>
    </Compile>
    <Compile Include="Register.aspx.designer.cs">
      <DependentUpon>Register.aspx</DependentUpon>
    </Compile>

    <Compile Include="Medicines.aspx.cs">
      <DependentUpon>Medicines.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Medicines.aspx.designer.cs">
      <DependentUpon>Medicines.aspx</DependentUpon>
    </Compile>
    <Compile Include="About.aspx.cs">
      <DependentUpon>About.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="About.aspx.designer.cs">
      <DependentUpon>About.aspx</DependentUpon>
    </Compile>
    <Compile Include="Contact.aspx.cs">
      <DependentUpon>Contact.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Contact.aspx.designer.cs">
      <DependentUpon>Contact.aspx</DependentUpon>
    </Compile>
    <Compile Include="FAQ.aspx.cs">
      <DependentUpon>FAQ.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="FAQ.aspx.designer.cs">
      <DependentUpon>FAQ.aspx</DependentUpon>
    </Compile>
    <Compile Include="ForgotPassword.aspx.cs">
      <DependentUpon>ForgotPassword.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="ForgotPassword.aspx.designer.cs">
      <DependentUpon>ForgotPassword.aspx</DependentUpon>
    </Compile>
    <Compile Include="ResetPassword.aspx.cs">
      <DependentUpon>ResetPassword.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="ResetPassword.aspx.designer.cs">
      <DependentUpon>ResetPassword.aspx</DependentUpon>
    </Compile>
    <Compile Include="Customer\Dashboard.aspx.cs">
      <DependentUpon>Dashboard.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Customer\Dashboard.aspx.designer.cs">
      <DependentUpon>Dashboard.aspx</DependentUpon>
    </Compile>
    <Compile Include="Site.Master.cs">
      <DependentUpon>Site.Master</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Site.Master.designer.cs">
      <DependentUpon>Site.Master</DependentUpon>
    </Compile>
    <Compile Include="Global.asax.cs">
      <DependentUpon>Global.asax</DependentUpon>
    </Compile>
    <Compile Include="Handlers\ChatHandler.ashx.cs">
      <DependentUpon>ChatHandler.ashx</DependentUpon>
    </Compile>
    <Compile Include="Models\User.cs" />
    <Compile Include="Models\Medicine.cs" />
    <Compile Include="Models\Order.cs" />
    <Compile Include="Models\Prescription.cs" />
    <Compile Include="Models\AdditionalModels.cs" />
    <Compile Include="DAL\MediEaseContext.cs" />
    <Compile Include="Utilities\AIHelper.cs" />
    <Compile Include="Utilities\DatabaseInitializer.cs" />
    <Compile Include="Utilities\SecurityHelper.cs" />
    <Compile Include="Utilities\ErrorHandlingModule.cs" />
    <Compile Include="Utilities\PaymentService.cs" />
    <Compile Include="Admin\SystemConfiguration.aspx.cs">
      <DependentUpon>SystemConfiguration.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Admin\BulkUpload.aspx.cs">
      <DependentUpon>BulkUpload.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Customer\HealthReminders.aspx.cs">
      <DependentUpon>HealthReminders.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pharmacist\InvoiceGeneration.aspx.cs">
      <DependentUpon>InvoiceGeneration.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="packages.config" />
    <None Include="Web.Debug.config">
      <DependentUpon>Web.config</DependentUpon>
    </None>
    <None Include="Web.Release.config">
      <DependentUpon>Web.config</DependentUpon>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Admin\" />
    <Folder Include="App_Data\" />
    <Folder Include="BLL\" />
    <Folder Include="Images\" />
    <Folder Include="Pharmacist\" />
  </ItemGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' != ''" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets" Condition="false" />
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <UseIIS>True</UseIIS>
          <AutoAssignPort>True</AutoAssignPort>
          <DevelopmentServerPort>44300</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl>https://localhost:44300/</IISUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>False</UseCustomServer>
          <CustomServerUrl>
          </CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\EntityFramework.6.5.1\build\EntityFramework.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\EntityFramework.6.5.1\build\EntityFramework.props'))" />
    <Error Condition="!Exists('..\packages\EntityFramework.6.5.1\build\EntityFramework.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\EntityFramework.6.5.1\build\EntityFramework.targets'))" />
  </Target>
  <Import Project="..\packages\EntityFramework.6.5.1\build\EntityFramework.targets" Condition="Exists('..\packages\EntityFramework.6.5.1\build\EntityFramework.targets')" />
</Project>