-- =============================================
-- MediEase Database Validation Tests
-- Comprehensive testing of all data operations
-- =============================================

USE MediEase;
GO

PRINT '==============================================';
PRINT 'Starting MediEase Database Validation Tests';
PRINT '==============================================';
PRINT '';

-- =============================================
-- 1. TABLE STRUCTURE VALIDATION
-- =============================================
PRINT '1. VALIDATING TABLE STRUCTURE...';

-- Check if all required tables exist
DECLARE @ExpectedTables INT = 25;
DECLARE @ActualTables INT;

SELECT @ActualTables = COUNT(*)
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_SCHEMA = 'dbo';

PRINT 'Expected Tables: ' + CAST(@ExpectedTables AS NVARCHAR);
PRINT 'Actual Tables: ' + CAST(@ActualTables AS NVARCHAR);

IF @ActualTables = @ExpectedTables
    PRINT '✓ All tables created successfully';
ELSE
    PRINT '✗ Missing tables detected';

-- List all tables
PRINT '';
PRINT 'Tables in database:';
SELECT TABLE_NAME, 
       (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = t.TABLE_NAME) as ColumnCount
FROM INFORMATION_SCHEMA.TABLES t
WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_SCHEMA = 'dbo'
ORDER BY TABLE_NAME;

-- =============================================
-- 2. FOREIGN KEY RELATIONSHIPS VALIDATION
-- =============================================
PRINT '';
PRINT '2. VALIDATING FOREIGN KEY RELATIONSHIPS...';

SELECT 
    fk.name AS ForeignKeyName,
    tp.name AS ParentTable,
    cp.name AS ParentColumn,
    tr.name AS ReferencedTable,
    cr.name AS ReferencedColumn
FROM sys.foreign_keys fk
INNER JOIN sys.tables tp ON fk.parent_object_id = tp.object_id
INNER JOIN sys.tables tr ON fk.referenced_object_id = tr.object_id
INNER JOIN sys.foreign_key_columns fkc ON fk.object_id = fkc.constraint_object_id
INNER JOIN sys.columns cp ON fkc.parent_column_id = cp.column_id AND fkc.parent_object_id = cp.object_id
INNER JOIN sys.columns cr ON fkc.referenced_column_id = cr.column_id AND fkc.referenced_object_id = cr.object_id
ORDER BY tp.name, fk.name;

-- =============================================
-- 3. SAMPLE DATA VALIDATION
-- =============================================
PRINT '';
PRINT '3. VALIDATING SAMPLE DATA...';

-- Check data counts in each table
DECLARE @TableName NVARCHAR(128);
DECLARE @SQL NVARCHAR(MAX);
DECLARE @RowCount INT;

DECLARE table_cursor CURSOR FOR
SELECT TABLE_NAME 
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_SCHEMA = 'dbo'
ORDER BY TABLE_NAME;

CREATE TABLE #TableCounts (
    TableName NVARCHAR(128),
    RowCount INT
);

OPEN table_cursor;
FETCH NEXT FROM table_cursor INTO @TableName;

WHILE @@FETCH_STATUS = 0
BEGIN
    SET @SQL = 'SELECT @Count = COUNT(*) FROM [' + @TableName + ']';
    EXEC sp_executesql @SQL, N'@Count INT OUTPUT', @Count = @RowCount OUTPUT;
    
    INSERT INTO #TableCounts (TableName, RowCount) VALUES (@TableName, @RowCount);
    
    FETCH NEXT FROM table_cursor INTO @TableName;
END

CLOSE table_cursor;
DEALLOCATE table_cursor;

-- Display table counts
SELECT TableName, RowCount,
       CASE 
           WHEN RowCount > 0 THEN '✓ Has Data'
           ELSE '✗ No Data'
       END as Status
FROM #TableCounts
ORDER BY TableName;

DROP TABLE #TableCounts;

-- =============================================
-- 4. USER AUTHENTICATION TESTS
-- =============================================
PRINT '';
PRINT '4. TESTING USER AUTHENTICATION...';

-- Test user login validation
DECLARE @TestEmail NVARCHAR(100) = '<EMAIL>';
DECLARE @TestPassword NVARCHAR(255) = '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi';

SELECT 
    UserId, 
    Email, 
    FirstName, 
    LastName, 
    Role, 
    IsActive, 
    LoyaltyPoints,
    'Authentication Test' as TestType
FROM Users 
WHERE Email = @TestEmail AND PasswordHash = @TestPassword AND IsActive = 1;

-- Test user profile retrieval
SELECT 
    UserId,
    Email,
    FirstName + ' ' + LastName as FullName,
    Role,
    Address + ', ' + City + ', ' + State as FullAddress,
    LoyaltyPoints,
    'Profile Retrieval Test' as TestType
FROM Users 
WHERE UserId = 1;

-- =============================================
-- 5. MEDICINE CATALOG TESTS
-- =============================================
PRINT '';
PRINT '5. TESTING MEDICINE CATALOG OPERATIONS...';

-- Test medicine search functionality
SELECT 
    m.MedicineId,
    m.Name,
    m.GenericName,
    c.Name as CategoryName,
    b.Name as BrandName,
    m.Price,
    m.StockQuantity,
    m.AverageRating,
    'Search Test - Tylenol' as TestType
FROM Medicines m
INNER JOIN Categories c ON m.CategoryId = c.CategoryId
LEFT JOIN Brands b ON m.BrandId = b.BrandId
WHERE m.Name LIKE '%Tylenol%' AND m.IsActive = 1;

-- Test featured medicines
SELECT 
    m.MedicineId,
    m.Name,
    m.Price,
    m.DiscountPercentage,
    (m.Price * (1 - m.DiscountPercentage / 100)) as FinalPrice,
    m.StockQuantity,
    m.AverageRating,
    'Featured Medicines Test' as TestType
FROM Medicines m
WHERE m.IsFeatured = 1 AND m.IsActive = 1 AND m.StockQuantity > 0
ORDER BY m.PurchaseCount DESC;

-- Test category-based filtering
SELECT 
    c.Name as CategoryName,
    COUNT(m.MedicineId) as MedicineCount,
    AVG(m.Price) as AveragePrice,
    SUM(m.StockQuantity) as TotalStock,
    'Category Analysis Test' as TestType
FROM Categories c
LEFT JOIN Medicines m ON c.CategoryId = m.CategoryId AND m.IsActive = 1
GROUP BY c.CategoryId, c.Name
ORDER BY MedicineCount DESC;

-- =============================================
-- 6. SHOPPING CART TESTS
-- =============================================
PRINT '';
PRINT '6. TESTING SHOPPING CART OPERATIONS...';

-- Test adding item to cart (simulate)
BEGIN TRANSACTION;

-- Add test item to cart
INSERT INTO ShoppingCart (UserId, MedicineId, Quantity, AddedDate)
VALUES (3, 1, 2, GETDATE());

-- Retrieve cart contents
SELECT 
    sc.CartId,
    sc.UserId,
    m.Name as MedicineName,
    sc.Quantity,
    m.Price as UnitPrice,
    (sc.Quantity * m.Price * (1 - m.DiscountPercentage / 100)) as TotalPrice,
    'Cart Contents Test' as TestType
FROM ShoppingCart sc
INNER JOIN Medicines m ON sc.MedicineId = m.MedicineId
WHERE sc.UserId = 3;

-- Calculate cart summary
SELECT 
    COUNT(*) as ItemCount,
    SUM(sc.Quantity) as TotalQuantity,
    SUM(sc.Quantity * m.Price * (1 - m.DiscountPercentage / 100)) as CartTotal,
    'Cart Summary Test' as TestType
FROM ShoppingCart sc
INNER JOIN Medicines m ON sc.MedicineId = m.MedicineId
WHERE sc.UserId = 3;

ROLLBACK TRANSACTION; -- Don't save test data

-- =============================================
-- 7. ORDER PROCESSING TESTS
-- =============================================
PRINT '';
PRINT '7. TESTING ORDER PROCESSING...';

-- Test order creation (simulate)
BEGIN TRANSACTION;

DECLARE @OrderId INT;
DECLARE @OrderNumber NVARCHAR(50) = 'TEST' + FORMAT(GETDATE(), 'yyyyMMddHHmmss');

-- Create test order
INSERT INTO Orders (OrderNumber, CustomerId, Status, OrderDate, Subtotal, TaxAmount, 
                   ShippingCost, TotalAmount, PaymentMethod, PaymentStatus,
                   ShippingAddress, ShippingCity, ShippingPostalCode, ContactPhone,
                   ContactEmail, CreatedDate)
VALUES (@OrderNumber, 3, 'Pending', GETDATE(), 25.98, 2.21, 5.99, 34.18, 'CreditCard', 'Pending',
        '123 Test Street', 'Test City', '12345', '******-TEST', '<EMAIL>', GETDATE());

SET @OrderId = SCOPE_IDENTITY();

-- Add order items
INSERT INTO OrderItems (OrderId, MedicineId, Quantity, UnitPrice, TotalPrice, CreatedDate)
VALUES 
    (@OrderId, 1, 1, 12.99, 12.99, GETDATE()),
    (@OrderId, 2, 1, 12.99, 12.99, GETDATE());

-- Test order retrieval
SELECT 
    o.OrderId,
    o.OrderNumber,
    o.Status,
    o.TotalAmount,
    u.FirstName + ' ' + u.LastName as CustomerName,
    COUNT(oi.OrderItemId) as ItemCount,
    'Order Test' as TestType
FROM Orders o
INNER JOIN Users u ON o.CustomerId = u.UserId
LEFT JOIN OrderItems oi ON o.OrderId = oi.OrderId
WHERE o.OrderId = @OrderId
GROUP BY o.OrderId, o.OrderNumber, o.Status, o.TotalAmount, u.FirstName, u.LastName;

-- Test order items retrieval
SELECT 
    oi.OrderItemId,
    m.Name as MedicineName,
    oi.Quantity,
    oi.UnitPrice,
    oi.TotalPrice,
    'Order Items Test' as TestType
FROM OrderItems oi
INNER JOIN Medicines m ON oi.MedicineId = m.MedicineId
WHERE oi.OrderId = @OrderId;

ROLLBACK TRANSACTION; -- Don't save test data

-- =============================================
-- 8. PRESCRIPTION MANAGEMENT TESTS
-- =============================================
PRINT '';
PRINT '8. TESTING PRESCRIPTION MANAGEMENT...';

-- Test prescription upload (simulate)
BEGIN TRANSACTION;

DECLARE @PrescriptionId INT;
DECLARE @PrescriptionNumber NVARCHAR(50) = 'RX' + FORMAT(GETDATE(), 'yyyyMMddHHmmss');

-- Create test prescription
INSERT INTO Prescriptions (UserId, PrescriptionNumber, DoctorName, PrescriptionDate,
                          OriginalFileName, FilePath, Status, CreatedDate)
VALUES (3, @PrescriptionNumber, 'Dr. Test Doctor', GETDATE(),
        'test_prescription.pdf', '/uploads/prescriptions/test.pdf', 'Pending', GETDATE());

SET @PrescriptionId = SCOPE_IDENTITY();

-- Test prescription retrieval
SELECT 
    p.PrescriptionId,
    p.PrescriptionNumber,
    p.DoctorName,
    p.Status,
    u.FirstName + ' ' + u.LastName as PatientName,
    'Prescription Test' as TestType
FROM Prescriptions p
INNER JOIN Users u ON p.UserId = u.UserId
WHERE p.PrescriptionId = @PrescriptionId;

ROLLBACK TRANSACTION; -- Don't save test data

-- =============================================
-- 9. LOYALTY PROGRAM TESTS
-- =============================================
PRINT '';
PRINT '9. TESTING LOYALTY PROGRAM...';

-- Test loyalty points calculation
SELECT 
    u.UserId,
    u.FirstName + ' ' + u.LastName as CustomerName,
    u.LoyaltyPoints as CurrentPoints,
    COUNT(lt.TransactionId) as TransactionCount,
    SUM(CASE WHEN lt.TransactionType = 'EARNED' THEN lt.Points ELSE 0 END) as TotalEarned,
    SUM(CASE WHEN lt.TransactionType = 'REDEEMED' THEN ABS(lt.Points) ELSE 0 END) as TotalRedeemed,
    'Loyalty Test' as TestType
FROM Users u
LEFT JOIN LoyaltyTransactions lt ON u.UserId = lt.UserId
WHERE u.Role = 'Customer'
GROUP BY u.UserId, u.FirstName, u.LastName, u.LoyaltyPoints;

-- =============================================
-- 10. INVENTORY MANAGEMENT TESTS
-- =============================================
PRINT '';
PRINT '10. TESTING INVENTORY MANAGEMENT...';

-- Test low stock detection
SELECT 
    m.MedicineId,
    m.Name,
    m.StockQuantity,
    m.MinStockLevel,
    c.Name as CategoryName,
    CASE 
        WHEN m.StockQuantity <= 0 THEN 'Out of Stock'
        WHEN m.StockQuantity <= m.MinStockLevel THEN 'Low Stock'
        ELSE 'In Stock'
    END as StockStatus,
    'Inventory Test' as TestType
FROM Medicines m
INNER JOIN Categories c ON m.CategoryId = c.CategoryId
WHERE m.IsActive = 1
ORDER BY m.StockQuantity ASC;
