-- =============================================
-- MediEase Local Database Creation Script
-- For creating MediEase.mdf in App_Data folder
-- =============================================

-- This script creates the database structure for LocalDB
-- The database file will be created automatically when the application runs

USE master;
GO

-- Create MediEase database for LocalDB
CREATE DATABASE [MediEase]
ON 
( NAME = N'MediEase', 
  FILENAME = N'|DataDirectory|\MediEase.mdf',
  SIZE = 100MB,
  MAXSIZE = 1GB,
  FILEGROWTH = 10MB )
LOG ON 
( NAME = N'MediEase_Log',
  FILENAME = N'|DataDirectory|\MediEase_Log.ldf',
  SIZE = 10MB,
  MAXSIZE = 100MB,
  FILEGROWTH = 10% );
GO

USE [MediEase];
GO

-- Enable snapshot isolation for better concurrency
ALTER DATABASE [MediEase] SET ALLOW_SNAPSHOT_ISOLATION ON;
ALTER DATABASE [MediEase] SET READ_COMMITTED_SNAPSHOT ON;
GO

-- =============================================
-- CORE TABLES (Essential for basic functionality)
-- =============================================

-- Users Table
CREATE TABLE [dbo].[Users] (
    [UserId] INT IDENTITY(1,1) PRIMARY KEY,
    [Email] NVARCHAR(100) NOT NULL UNIQUE,
    [PasswordHash] NVARCHAR(255) NOT NULL,
    [FirstName] NVARCHAR(50) NOT NULL,
    [LastName] NVARCHAR(50) NOT NULL,
    [PhoneNumber] NVARCHAR(20),
    [Role] NVARCHAR(20) NOT NULL DEFAULT 'Customer',
    [IsActive] BIT NOT NULL DEFAULT 1,
    [IsEmailVerified] BIT NOT NULL DEFAULT 0,
    [Address] NVARCHAR(500),
    [City] NVARCHAR(100),
    [State] NVARCHAR(100),
    [PostalCode] NVARCHAR(20),
    [Country] NVARCHAR(100) DEFAULT 'USA',
    [DateOfBirth] DATE,
    [Gender] NVARCHAR(10),
    [LoyaltyPoints] INT DEFAULT 0,
    [CreatedDate] DATETIME NOT NULL DEFAULT GETDATE(),
    [ModifiedDate] DATETIME
);

-- Categories Table
CREATE TABLE [dbo].[Categories] (
    [CategoryId] INT IDENTITY(1,1) PRIMARY KEY,
    [Name] NVARCHAR(100) NOT NULL UNIQUE,
    [Description] NVARCHAR(500),
    [ImageUrl] NVARCHAR(255),
    [IsActive] BIT NOT NULL DEFAULT 1,
    [CreatedDate] DATETIME NOT NULL DEFAULT GETDATE()
);

-- Brands Table
CREATE TABLE [dbo].[Brands] (
    [BrandId] INT IDENTITY(1,1) PRIMARY KEY,
    [Name] NVARCHAR(100) NOT NULL UNIQUE,
    [Description] NVARCHAR(500),
    [LogoUrl] NVARCHAR(255),
    [IsActive] BIT NOT NULL DEFAULT 1,
    [CreatedDate] DATETIME NOT NULL DEFAULT GETDATE()
);

-- Medicines Table
CREATE TABLE [dbo].[Medicines] (
    [MedicineId] INT IDENTITY(1,1) PRIMARY KEY,
    [Name] NVARCHAR(200) NOT NULL,
    [GenericName] NVARCHAR(200),
    [Description] NVARCHAR(2000),
    [CategoryId] INT NOT NULL,
    [BrandId] INT,
    [Price] DECIMAL(10,2) NOT NULL,
    [DiscountPercentage] DECIMAL(5,2) DEFAULT 0,
    [StockQuantity] INT NOT NULL DEFAULT 0,
    [MinStockLevel] INT DEFAULT 10,
    [PrescriptionRequired] BIT NOT NULL DEFAULT 0,
    [Dosage] NVARCHAR(100),
    [DosageForm] NVARCHAR(50),
    [Strength] NVARCHAR(50),
    [IsFeatured] BIT NOT NULL DEFAULT 0,
    [AverageRating] DECIMAL(3,2) DEFAULT 0,
    [ReviewCount] INT DEFAULT 0,
    [PurchaseCount] INT DEFAULT 0,
    [ImageUrl] NVARCHAR(255),
    [IsActive] BIT NOT NULL DEFAULT 1,
    [CreatedDate] DATETIME NOT NULL DEFAULT GETDATE(),
    [ModifiedDate] DATETIME,
    
    FOREIGN KEY ([CategoryId]) REFERENCES [Categories]([CategoryId]),
    FOREIGN KEY ([BrandId]) REFERENCES [Brands]([BrandId])
);

-- Orders Table
CREATE TABLE [dbo].[Orders] (
    [OrderId] INT IDENTITY(1,1) PRIMARY KEY,
    [OrderNumber] NVARCHAR(50) NOT NULL UNIQUE,
    [CustomerId] INT NOT NULL,
    [Status] NVARCHAR(20) NOT NULL DEFAULT 'Pending',
    [OrderDate] DATETIME NOT NULL DEFAULT GETDATE(),
    [Subtotal] DECIMAL(10,2) NOT NULL,
    [TaxAmount] DECIMAL(10,2) DEFAULT 0,
    [ShippingCost] DECIMAL(10,2) DEFAULT 0,
    [DiscountAmount] DECIMAL(10,2) DEFAULT 0,
    [TotalAmount] DECIMAL(10,2) NOT NULL,
    [PaymentMethod] NVARCHAR(50),
    [PaymentStatus] NVARCHAR(20) DEFAULT 'Pending',
    [PaymentTransactionId] NVARCHAR(100),
    [PaymentDate] DATETIME,
    [InvoiceGenerated] BIT DEFAULT 0,
    [InvoiceDate] DATETIME,
    [ShippingAddress] NVARCHAR(500) NOT NULL,
    [ShippingCity] NVARCHAR(100),
    [ShippingPostalCode] NVARCHAR(20),
    [ContactPhone] NVARCHAR(20),
    [ContactEmail] NVARCHAR(100),
    [RequiresPrescription] BIT DEFAULT 0,
    [PrescriptionVerified] BIT DEFAULT 0,
    [CreatedDate] DATETIME NOT NULL DEFAULT GETDATE(),
    [ModifiedDate] DATETIME,
    
    FOREIGN KEY ([CustomerId]) REFERENCES [Users]([UserId])
);

-- Order Items Table
CREATE TABLE [dbo].[OrderItems] (
    [OrderItemId] INT IDENTITY(1,1) PRIMARY KEY,
    [OrderId] INT NOT NULL,
    [MedicineId] INT NOT NULL,
    [Quantity] INT NOT NULL,
    [UnitPrice] DECIMAL(10,2) NOT NULL,
    [DiscountAmount] DECIMAL(10,2) DEFAULT 0,
    [TotalPrice] DECIMAL(10,2) NOT NULL,
    [CreatedDate] DATETIME NOT NULL DEFAULT GETDATE(),
    
    FOREIGN KEY ([OrderId]) REFERENCES [Orders]([OrderId]) ON DELETE CASCADE,
    FOREIGN KEY ([MedicineId]) REFERENCES [Medicines]([MedicineId])
);

-- Shopping Cart Table
CREATE TABLE [dbo].[ShoppingCart] (
    [CartId] INT IDENTITY(1,1) PRIMARY KEY,
    [UserId] INT NOT NULL,
    [MedicineId] INT NOT NULL,
    [Quantity] INT NOT NULL,
    [AddedDate] DATETIME NOT NULL DEFAULT GETDATE(),
    
    FOREIGN KEY ([UserId]) REFERENCES [Users]([UserId]) ON DELETE CASCADE,
    FOREIGN KEY ([MedicineId]) REFERENCES [Medicines]([MedicineId]),
    UNIQUE ([UserId], [MedicineId])
);

-- Prescriptions Table
CREATE TABLE [dbo].[Prescriptions] (
    [PrescriptionId] INT IDENTITY(1,1) PRIMARY KEY,
    [UserId] INT NOT NULL,
    [PrescriptionNumber] NVARCHAR(50) NOT NULL UNIQUE,
    [DoctorName] NVARCHAR(100) NOT NULL,
    [DoctorLicense] NVARCHAR(50),
    [PrescriptionDate] DATE NOT NULL,
    [ExpiryDate] DATE,
    [OriginalFileName] NVARCHAR(255),
    [FilePath] NVARCHAR(500),
    [Status] NVARCHAR(20) DEFAULT 'Pending',
    [VerifiedBy] INT,
    [VerificationDate] DATETIME,
    [CreatedDate] DATETIME NOT NULL DEFAULT GETDATE(),
    
    FOREIGN KEY ([UserId]) REFERENCES [Users]([UserId]),
    FOREIGN KEY ([VerifiedBy]) REFERENCES [Users]([UserId])
);

-- =============================================
-- ESSENTIAL INDEXES FOR PERFORMANCE
-- =============================================

CREATE INDEX IX_Users_Email ON [Users]([Email]);
CREATE INDEX IX_Users_Role ON [Users]([Role]);
CREATE INDEX IX_Medicines_Category ON [Medicines]([CategoryId]);
CREATE INDEX IX_Medicines_Active ON [Medicines]([IsActive]);
CREATE INDEX IX_Orders_Customer ON [Orders]([CustomerId]);
CREATE INDEX IX_Orders_Status ON [Orders]([Status]);
CREATE INDEX IX_Orders_Date ON [Orders]([OrderDate]);

-- =============================================
-- INITIAL DATA
-- =============================================

-- Insert Categories
INSERT INTO [Categories] ([Name], [Description]) VALUES
('Pain Relief', 'Medications for pain management'),
('Antibiotics', 'Prescription antibiotics'),
('Vitamins', 'Vitamins and supplements'),
('Cold & Flu', 'Cold and flu medications'),
('Digestive Health', 'Digestive medications');

-- Insert Brands
INSERT INTO [Brands] ([Name], [Description]) VALUES
('Generic', 'Generic medications'),
('Pfizer', 'Pfizer pharmaceuticals'),
('Johnson & Johnson', 'J&J healthcare products');

-- Insert Sample Medicines
INSERT INTO [Medicines] ([Name], [GenericName], [Description], [CategoryId], [BrandId], [Price], [StockQuantity], [PrescriptionRequired], [Dosage], [IsFeatured]) VALUES
('Tylenol', 'Acetaminophen', 'Pain relief medication', 1, 3, 12.99, 100, 0, '500mg', 1),
('Advil', 'Ibuprofen', 'Anti-inflammatory pain relief', 1, 3, 15.49, 150, 0, '200mg', 1),
('Vitamin C', 'Ascorbic Acid', 'Immune system support', 3, 1, 8.99, 200, 0, '1000mg', 0),
('Robitussin', 'Dextromethorphan', 'Cough suppressant', 4, 3, 11.99, 80, 0, '15mg/5ml', 0);

-- Insert Initial Admin User (Change password before production!)
INSERT INTO [Users] ([Email], [PasswordHash], [FirstName], [LastName], [Role], [IsActive], [IsEmailVerified]) VALUES
('<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'System', 'Administrator', 'Admin', 1, 1);

GO

PRINT 'MediEase LocalDB database created successfully!';
PRINT 'Database file: MediEase.mdf will be created in App_Data folder';
PRINT 'Remember to change the default admin password!';
