using System;
using System.IO;
using System.Linq;
using System.Web.UI;
using System.Web.UI.WebControls;
using MediEase.DAL;
using MediEase.Models;
using MediEase.Utilities;

namespace MediEase
{
    public partial class Prescription : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                LoadUserData();
                LoadRecentPrescriptions();
            }
        }

        private void LoadUserData()
        {
            var currentUser = SecurityHelper.GetCurrentUser();
            if (currentUser != null)
            {
                txtPatientName.Text = currentUser.FullName;
                txtPhoneNumber.Text = currentUser.PhoneNumber;
                txtDeliveryAddress.Text = currentUser.Address;
                pnlRecentPrescriptions.Visible = true;
            }
        }

        private void LoadRecentPrescriptions()
        {
            var currentUser = SecurityHelper.GetCurrentUser();
            if (currentUser != null)
            {
                try
                {
                    using (var db = new MediEaseContext())
                    {
                        var recentPrescriptions = db.Prescriptions
                            .Where(p => p.PatientId == currentUser.UserId)
                            .OrderByDescending(p => p.CreatedDate)
                            .Take(5)
                            .Select(p => new {
                                p.PrescriptionNumber,
                                p.CreatedDate,
                                p.Status
                            })
                            .ToList();

                        if (recentPrescriptions.Any())
                        {
                            rptRecentPrescriptions.DataSource = recentPrescriptions;
                            rptRecentPrescriptions.DataBind();
                            pnlRecentPrescriptions.Visible = true;
                        }
                    }
                }
                catch (Exception ex)
                {
                    ErrorLogger.LogError(ex, "LoadRecentPrescriptions");
                }
            }
        }

        protected void btnUpload_Click(object sender, EventArgs e)
        {
            try
            {
                if (!ValidateForm())
                    return;

                if (!fuPrescription.HasFile)
                {
                    ShowError("Please select a prescription file to upload.");
                    return;
                }

                // Validate file
                if (!ValidateFile())
                    return;

                // Save prescription
                var prescriptionId = SavePrescription();
                if (prescriptionId > 0)
                {
                    ShowSuccess(prescriptionId);
                }
                else
                {
                    ShowError("Failed to upload prescription. Please try again.");
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "btnUpload_Click");
                ShowError("An error occurred while uploading your prescription. Please try again.");
            }
        }

        private bool ValidateForm()
        {
            if (string.IsNullOrWhiteSpace(txtPatientName.Text))
            {
                ShowError("Patient name is required.");
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtPhoneNumber.Text))
            {
                ShowError("Phone number is required.");
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtDeliveryAddress.Text))
            {
                ShowError("Delivery address is required.");
                return false;
            }

            return true;
        }

        private bool ValidateFile()
        {
            var allowedExtensions = new[] { ".jpg", ".jpeg", ".png", ".pdf" };
            var fileExtension = Path.GetExtension(fuPrescription.FileName).ToLower();

            if (!allowedExtensions.Contains(fileExtension))
            {
                ShowError("Please upload a valid file format (JPG, PNG, or PDF).");
                return false;
            }

            if (fuPrescription.PostedFile.ContentLength > 5 * 1024 * 1024) // 5MB
            {
                ShowError("File size must be less than 5MB.");
                return false;
            }

            return true;
        }

        private int SavePrescription()
        {
            try
            {
                using (var db = new MediEaseContext())
                {
                    var currentUser = SecurityHelper.GetCurrentUser();

                    // Create prescription record
                    var prescription = new Models.Prescription
                    {
                        PrescriptionNumber = GeneratePrescriptionNumber(),
                        PatientId = currentUser?.UserId ?? 0,
                        DoctorName = SecurityHelper.SanitizeInput(txtDoctorName.Text.Trim()),
                        Notes = SecurityHelper.SanitizeInput(txtNotes.Text.Trim()),
                        IsEmergency = chkUrgent.Checked,
                        Status = "Pending",
                        PrescriptionDate = DateTime.Now,
                        ValidUntil = DateTime.Now.AddMonths(6), // Default 6 months validity
                        CreatedDate = DateTime.Now,
                        CreatedBy = currentUser?.UserId
                    };

                    // Save file
                    var fileName = SavePrescriptionFile();
                    if (!string.IsNullOrEmpty(fileName))
                    {
                        prescription.PrescriptionImage = fileName;
                    }

                    db.Prescriptions.Add(prescription);
                    db.SaveChanges();

                    // Log successful upload
                    ErrorLogger.LogInfo($"Prescription uploaded successfully. ID: {prescription.PrescriptionId}, Number: {prescription.PrescriptionNumber}", "SavePrescription");

                    return prescription.PrescriptionId;
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "SavePrescription");
                return 0;
            }
        }

        private string GeneratePrescriptionNumber()
        {
            return "RX" + DateTime.Now.ToString("yyyyMMdd") + new Random().Next(1000, 9999).ToString();
        }

        private string SavePrescriptionFile()
        {
            try
            {
                var uploadsPath = Server.MapPath("~/Uploads/Prescriptions/");
                if (!Directory.Exists(uploadsPath))
                {
                    Directory.CreateDirectory(uploadsPath);
                }

                var fileExtension = Path.GetExtension(fuPrescription.FileName);
                var fileName = $"prescription_{DateTime.Now:yyyyMMddHHmmss}_{Guid.NewGuid().ToString("N").Substring(0, 8)}{fileExtension}";
                var filePath = Path.Combine(uploadsPath, fileName);

                fuPrescription.SaveAs(filePath);
                return fileName;
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "SavePrescriptionFile");
                return null;
            }
        }

        private void ShowSuccess(int prescriptionId)
        {
            using (var db = new MediEaseContext())
            {
                var prescription = db.Prescriptions.Find(prescriptionId);
                if (prescription != null)
                {
                    litReferenceId.Text = prescription.PrescriptionNumber;
                }
                else
                {
                    litReferenceId.Text = $"RX{prescriptionId:D6}";
                }
            }

            litContactNumber.Text = txtPhoneNumber.Text;
            pnlUpload.Visible = false;
            pnlSuccess.Visible = true;
            pnlRecentPrescriptions.Visible = false;
        }

        private void ShowError(string message)
        {
            ScriptManager.RegisterStartupScript(this, GetType(), "error",
                $"alert('{message.Replace("'", "\\'")}');", true);
        }

        protected void btnClear_Click(object sender, EventArgs e)
        {
            ClearForm();
        }

        protected void btnUploadAnother_Click(object sender, EventArgs e)
        {
            pnlUpload.Visible = true;
            pnlSuccess.Visible = false;
            pnlRecentPrescriptions.Visible = true;
            ClearForm();
            LoadRecentPrescriptions();
        }

        private void ClearForm()
        {
            txtDoctorName.Text = "";
            txtNotes.Text = "";
            chkUrgent.Checked = false;
            
            // Don't clear user data if logged in
            var currentUser = SecurityHelper.GetCurrentUser();
            if (currentUser == null)
            {
                txtPatientName.Text = "";
                txtPhoneNumber.Text = "";
                txtDeliveryAddress.Text = "";
            }
        }

        protected string GetStatusColor(string status)
        {
            switch (status?.ToLower())
            {
                case "pending review":
                    return "warning";
                case "approved":
                    return "success";
                case "rejected":
                    return "danger";
                case "processing":
                    return "info";
                case "completed":
                    return "success";
                default:
                    return "secondary";
            }
        }
    }
}
