# 📋 MediEase Database Table Creation Guide

## 🎯 Quick Setup Instructions

### Step 1: Connect to Your Database
1. Open **SQL Server Management Studio (SSMS)**
2. Connect to **(LocalDB)\MSSQLLocalDB**
3. Right-click on **Databases** → **Attach...**
4. Browse to your `App_Data` folder and select `MediEase.mdf`
5. Click **OK** to attach the database

### Step 2: Create Tables in Order
**⚠️ IMPORTANT: Create tables in this exact order to avoid foreign key errors!**

## 📊 Table Creation Order

### 1. **Core Tables (No Dependencies)**
```sql
-- Create these first:
Users
Categories  
Brands
SystemSettings
```

### 2. **Product Tables**
```sql
-- Create after Categories and Brands:
Medicines
MedicineImages
MedicineInteractions
```

### 3. **Order Tables**
```sql
-- Create after Users and Medicines:
Orders
OrderItems
ShoppingCart
```

### 4. **Medical Tables**
```sql
-- Create after Users:
Prescriptions
PrescriptionItems
FamilyProfiles
HealthReminders
```

### 5. **Review and Loyalty Tables**
```sql
-- Create after Users, Medicines, and Orders:
MedicineReviews
LoyaltyTransactions
Offers
UserOfferUsage
```

### 6. **Communication Tables**
```sql
-- Create after Users:
Notifications
ChatSessions
ChatMessages
AIRecommendations
```

### 7. **System Tables**
```sql
-- Create after Users:
StockMovements
AuditLogs
ErrorLogs
```

## 🔑 Essential Tables for Basic Functionality

If you want to start with just the core functionality, create these tables first:

### **Minimum Required Tables:**
1. **Users** - User authentication
2. **Categories** - Medicine categories
3. **Brands** - Medicine brands
4. **Medicines** - Product catalog
5. **Orders** - Customer orders
6. **OrderItems** - Order details
7. **ShoppingCart** - Shopping cart
8. **Prescriptions** - Prescription uploads

### **Additional Tables for Full Features:**
- **FamilyProfiles** - Family member management
- **HealthReminders** - Medication reminders
- **MedicineReviews** - Product reviews
- **LoyaltyTransactions** - Loyalty points
- **Notifications** - User notifications
- **ChatSessions/ChatMessages** - AI chatbot
- **SystemSettings** - Application configuration

## 📝 Table Relationships Summary

### **Primary Relationships:**
- `Users` → `Orders` (Customer orders)
- `Users` → `Prescriptions` (User prescriptions)
- `Categories` → `Medicines` (Medicine categorization)
- `Brands` → `Medicines` (Medicine branding)
- `Orders` → `OrderItems` (Order details)
- `Medicines` → `OrderItems` (Products in orders)
- `Users` → `ShoppingCart` (User cart items)
- `Medicines` → `ShoppingCart` (Products in cart)

### **Secondary Relationships:**
- `Users` → `FamilyProfiles` (Family members)
- `Users` → `HealthReminders` (User reminders)
- `Medicines` → `MedicineReviews` (Product reviews)
- `Users` → `LoyaltyTransactions` (Points tracking)
- `Users` → `ChatSessions` (AI chat history)

## 🔧 Column Types Reference

### **Common Data Types Used:**
- `INT IDENTITY(1,1)` - Auto-incrementing primary keys
- `NVARCHAR(50)` - Short text fields
- `NVARCHAR(500)` - Medium text fields
- `NVARCHAR(MAX)` - Large text fields
- `DECIMAL(10,2)` - Currency/price fields
- `BIT` - Boolean fields (0/1)
- `DATETIME` - Date and time fields
- `DATE` - Date only fields

### **Key Constraints:**
- `PRIMARY KEY` - Unique identifier
- `FOREIGN KEY` - References other tables
- `UNIQUE` - Ensures uniqueness
- `NOT NULL` - Required fields
- `DEFAULT` - Default values
- `CHECK` - Value validation

## 🚀 Quick Start SQL Commands

### **Create Database (if needed):**
```sql
CREATE DATABASE MediEase;
USE MediEase;
```

### **Check Table Creation:**
```sql
-- List all tables
SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_TYPE = 'BASE TABLE' 
ORDER BY TABLE_NAME;

-- Count records in each table
SELECT 
    t.TABLE_NAME,
    p.rows as RowCount
FROM INFORMATION_SCHEMA.TABLES t
LEFT JOIN sys.partitions p ON OBJECT_ID(t.TABLE_NAME) = p.object_id
WHERE t.TABLE_TYPE = 'BASE TABLE' AND p.index_id IN (0,1)
ORDER BY t.TABLE_NAME;
```

### **Test Basic Functionality:**
```sql
-- Check if sample data was inserted
SELECT COUNT(*) as UserCount FROM Users;
SELECT COUNT(*) as CategoryCount FROM Categories;
SELECT COUNT(*) as MedicineCount FROM Medicines;
SELECT COUNT(*) as BrandCount FROM Brands;
```

## ⚠️ Important Notes

### **Before Production:**
1. **Change Default Admin Password** in Users table
2. **Update System Settings** with your company information
3. **Configure Proper Backup** procedures
4. **Set Appropriate Permissions** on database files
5. **Test All Functionality** thoroughly

### **Security Considerations:**
- Never use default passwords in production
- Regularly backup your database
- Monitor error logs for issues
- Keep software updated
- Use strong passwords for all accounts

### **Performance Tips:**
- Indexes are automatically created for optimal performance
- Monitor database size and growth
- Archive old data periodically
- Optimize queries as needed

## 📞 Troubleshooting

### **Common Issues:**
1. **Foreign Key Errors** - Create tables in the correct order
2. **Permission Denied** - Ensure proper database permissions
3. **File Not Found** - Check App_Data folder permissions
4. **Connection Failed** - Verify LocalDB is running

### **Verification Steps:**
1. Check if all tables were created successfully
2. Verify sample data was inserted
3. Test basic queries on each table
4. Confirm foreign key relationships work

---

**🎉 Once all tables are created, your MediEase database will be ready to support the full pharmacy management system with AI integration!**
