# 📊 MediEase SQL Query Reference Guide

## 🎯 **Complete SQL Package Overview**

### **📁 Files Provided:**
1. **`MediEase_Complete_SQL_Queries.sql`** - All basic CRUD operations
2. **`MediEase_Advanced_SQL_Queries.sql`** - Advanced features and analytics
3. **`MediEase_Stored_Procedures.sql`** - Optimized procedures for common operations

## 🔍 **Query Categories**

### **1. User Authentication & Management**
```sql
-- User Registration
INSERT INTO Users (Email, PasswordHash, FirstName, LastName, Role, CreatedDate)
VALUES (@Email, @PasswordHash, @FirstName, @LastName, 'Customer', GETDATE());

-- User Login
SELECT UserId, Email, PasswordHash, FirstName, LastName, Role, LoyaltyPoints
FROM Users WHERE Email = @Email AND IsActive = 1;

-- Update Profile
UPDATE Users SET FirstName = @FirstName, LastName = @LastName, 
PhoneNumber = @PhoneNumber WHERE UserId = @UserId;
```

### **2. Medicine Catalog Management**
```sql
-- Search Medicines
SELECT m.*, c.Name as CategoryName, b.Name as BrandName
FROM Medicines m
INNER JOIN Categories c ON m.CategoryId = c.CategoryId
LEFT JOIN Brands b ON m.BrandId = b.BrandId
WHERE m.Name LIKE '%' + @SearchTerm + '%' AND m.IsActive = 1;

-- Get Featured Medicines
SELECT * FROM Medicines 
WHERE IsFeatured = 1 AND IsActive = 1 AND StockQuantity > 0
ORDER BY PurchaseCount DESC;

-- Add New Medicine
INSERT INTO Medicines (Name, CategoryId, Price, StockQuantity, CreatedDate)
VALUES (@Name, @CategoryId, @Price, @StockQuantity, GETDATE());
```

### **3. Shopping Cart Operations**
```sql
-- Add to Cart
INSERT INTO ShoppingCart (UserId, MedicineId, Quantity, AddedDate)
VALUES (@UserId, @MedicineId, @Quantity, GETDATE());

-- Get Cart Items
SELECT sc.*, m.Name, m.Price, (sc.Quantity * m.Price) as TotalPrice
FROM ShoppingCart sc
INNER JOIN Medicines m ON sc.MedicineId = m.MedicineId
WHERE sc.UserId = @UserId;

-- Clear Cart
DELETE FROM ShoppingCart WHERE UserId = @UserId;
```

### **4. Order Processing**
```sql
-- Create Order
INSERT INTO Orders (OrderNumber, CustomerId, Status, TotalAmount, CreatedDate)
VALUES (@OrderNumber, @CustomerId, 'Pending', @TotalAmount, GETDATE());

-- Add Order Items
INSERT INTO OrderItems (OrderId, MedicineId, Quantity, UnitPrice, TotalPrice)
VALUES (@OrderId, @MedicineId, @Quantity, @UnitPrice, @TotalPrice);

-- Update Order Status
UPDATE Orders SET Status = @Status, ModifiedDate = GETDATE()
WHERE OrderId = @OrderId;
```

### **5. Prescription Management**
```sql
-- Upload Prescription
INSERT INTO Prescriptions (UserId, PrescriptionNumber, DoctorName, 
PrescriptionDate, FilePath, Status, CreatedDate)
VALUES (@UserId, @PrescriptionNumber, @DoctorName, 
@PrescriptionDate, @FilePath, 'Pending', GETDATE());

-- Verify Prescription
UPDATE Prescriptions SET Status = 'Verified', VerifiedBy = @VerifiedBy,
VerificationDate = GETDATE() WHERE PrescriptionId = @PrescriptionId;
```

### **6. Inventory Management**
```sql
-- Update Stock
UPDATE Medicines SET StockQuantity = @NewQuantity, ModifiedDate = GETDATE()
WHERE MedicineId = @MedicineId;

-- Low Stock Alert
SELECT * FROM Medicines 
WHERE StockQuantity <= MinStockLevel AND IsActive = 1
ORDER BY StockQuantity ASC;

-- Stock Movement Tracking
INSERT INTO StockMovements (MedicineId, MovementType, Quantity, 
PreviousStock, NewStock, CreatedDate)
VALUES (@MedicineId, @MovementType, @Quantity, 
@PreviousStock, @NewStock, GETDATE());
```

### **7. Loyalty Program**
```sql
-- Add Loyalty Points
UPDATE Users SET LoyaltyPoints = LoyaltyPoints + @Points
WHERE UserId = @UserId;

-- Record Loyalty Transaction
INSERT INTO LoyaltyTransactions (UserId, TransactionType, Points, Description)
VALUES (@UserId, 'EARNED', @Points, @Description);

-- Redeem Points
UPDATE Users SET LoyaltyPoints = LoyaltyPoints - @Points
WHERE UserId = @UserId;
```

### **8. Reviews & Ratings**
```sql
-- Add Review
INSERT INTO MedicineReviews (MedicineId, UserId, Rating, Title, ReviewText)
VALUES (@MedicineId, @UserId, @Rating, @Title, @ReviewText);

-- Update Medicine Rating
UPDATE Medicines SET AverageRating = (
    SELECT AVG(CAST(Rating as DECIMAL(3,2))) 
    FROM MedicineReviews WHERE MedicineId = @MedicineId
) WHERE MedicineId = @MedicineId;
```

## 🚀 **Stored Procedures**

### **User Management**
```sql
-- Register User
EXEC sp_RegisterUser @Email, @PasswordHash, @FirstName, @LastName, @PhoneNumber;

-- Authenticate User
EXEC sp_AuthenticateUser @Email, @PasswordHash;
```

### **Shopping Cart**
```sql
-- Add to Cart with Validation
EXEC sp_AddToCart @UserId, @MedicineId, @Quantity;
```

### **Order Processing**
```sql
-- Create Order from Cart
EXEC sp_CreateOrderFromCart @UserId, @ShippingAddress, @PaymentMethod;
```

### **Inventory Management**
```sql
-- Update Stock with Movement Tracking
EXEC sp_UpdateStock @MedicineId, @Quantity, @MovementType, @Reference;
```

### **Prescription Processing**
```sql
-- Process Prescription Upload
EXEC sp_ProcessPrescription @UserId, @DoctorName, @PrescriptionDate, @FilePath;
```

### **Reporting**
```sql
-- Generate Sales Report
EXEC sp_GenerateSalesReport @StartDate, @EndDate, @GroupBy;

-- Get Dashboard Statistics
EXEC sp_GetDashboardStats @UserId, @Role;
```

## 📊 **Advanced Analytics Queries**

### **Sales Analytics**
```sql
-- Daily Sales Report
SELECT CAST(OrderDate as DATE) as SaleDate,
       COUNT(*) as TotalOrders,
       SUM(TotalAmount) as TotalRevenue
FROM Orders WHERE Status NOT IN ('Cancelled')
GROUP BY CAST(OrderDate as DATE);

-- Top Selling Medicines
SELECT m.Name, SUM(oi.Quantity) as TotalSold
FROM OrderItems oi
INNER JOIN Medicines m ON oi.MedicineId = m.MedicineId
GROUP BY m.Name ORDER BY TotalSold DESC;
```

### **Customer Analytics**
```sql
-- Customer Lifetime Value
SELECT u.FirstName, u.LastName, 
       COUNT(o.OrderId) as TotalOrders,
       SUM(o.TotalAmount) as LifetimeValue
FROM Users u
LEFT JOIN Orders o ON u.UserId = o.CustomerId
WHERE u.Role = 'Customer'
GROUP BY u.UserId, u.FirstName, u.LastName;

-- Customer Segmentation
SELECT 
    CASE 
        WHEN TotalSpent >= 1000 THEN 'VIP'
        WHEN TotalSpent >= 500 THEN 'Premium'
        ELSE 'Regular'
    END as Segment,
    COUNT(*) as CustomerCount
FROM (SELECT SUM(TotalAmount) as TotalSpent FROM Orders GROUP BY CustomerId) t
GROUP BY CASE WHEN TotalSpent >= 1000 THEN 'VIP' 
              WHEN TotalSpent >= 500 THEN 'Premium' 
              ELSE 'Regular' END;
```

### **Inventory Analytics**
```sql
-- Stock Valuation
SELECT m.Name, m.StockQuantity, m.CostPrice,
       (m.StockQuantity * m.CostPrice) as StockValue
FROM Medicines m WHERE m.IsActive = 1;

-- Fast Moving Items
SELECT m.Name, SUM(oi.Quantity) as TotalSold
FROM Medicines m
INNER JOIN OrderItems oi ON m.MedicineId = oi.MedicineId
INNER JOIN Orders o ON oi.OrderId = o.OrderId
WHERE o.OrderDate >= DATEADD(DAY, -30, GETDATE())
GROUP BY m.Name ORDER BY TotalSold DESC;
```

## 🔧 **Utility Queries**

### **Database Maintenance**
```sql
-- Check Table Sizes
SELECT t.NAME AS TableName,
       p.rows AS RowCounts
FROM sys.tables t
INNER JOIN sys.partitions p ON t.object_id = p.object_id
WHERE p.index_id < 2
ORDER BY p.rows DESC;

-- Find Unused Indexes
SELECT i.name AS IndexName, t.name AS TableName
FROM sys.indexes i
INNER JOIN sys.tables t ON i.object_id = t.object_id
LEFT JOIN sys.dm_db_index_usage_stats s ON i.object_id = s.object_id 
    AND i.index_id = s.index_id
WHERE s.index_id IS NULL AND i.index_id > 0;
```

### **Performance Monitoring**
```sql
-- Slow Queries Detection
SELECT TOP 10 
    total_elapsed_time/execution_count AS avg_elapsed_time,
    execution_count,
    SUBSTRING(st.text, (qs.statement_start_offset/2)+1,
        ((CASE qs.statement_end_offset
            WHEN -1 THEN DATALENGTH(st.text)
            ELSE qs.statement_end_offset
        END - qs.statement_start_offset)/2) + 1) AS statement_text
FROM sys.dm_exec_query_stats qs
CROSS APPLY sys.dm_exec_sql_text(qs.sql_handle) st
ORDER BY avg_elapsed_time DESC;
```

## 🔍 **Search and Filtering**

### **Advanced Medicine Search**
```sql
SELECT m.*, c.Name as CategoryName, b.Name as BrandName
FROM Medicines m
INNER JOIN Categories c ON m.CategoryId = c.CategoryId
LEFT JOIN Brands b ON m.BrandId = b.BrandId
WHERE m.IsActive = 1
  AND (@SearchTerm IS NULL OR m.Name LIKE '%' + @SearchTerm + '%')
  AND (@CategoryId IS NULL OR m.CategoryId = @CategoryId)
  AND (@MinPrice IS NULL OR m.Price >= @MinPrice)
  AND (@MaxPrice IS NULL OR m.Price <= @MaxPrice)
ORDER BY m.Name;
```

### **Order Filtering**
```sql
SELECT o.*, u.FirstName, u.LastName
FROM Orders o
INNER JOIN Users u ON o.CustomerId = u.UserId
WHERE (@Status IS NULL OR o.Status = @Status)
  AND (@StartDate IS NULL OR o.OrderDate >= @StartDate)
  AND (@EndDate IS NULL OR o.OrderDate <= @EndDate)
  AND (@CustomerId IS NULL OR o.CustomerId = @CustomerId)
ORDER BY o.OrderDate DESC;
```

## 📈 **Business Intelligence Queries**

### **Revenue Analysis**
```sql
-- Monthly Revenue Trend
SELECT YEAR(OrderDate) as Year, MONTH(OrderDate) as Month,
       SUM(TotalAmount) as Revenue,
       COUNT(*) as OrderCount
FROM Orders WHERE Status NOT IN ('Cancelled')
GROUP BY YEAR(OrderDate), MONTH(OrderDate)
ORDER BY Year, Month;

-- Category Performance
SELECT c.Name, SUM(oi.TotalPrice) as Revenue,
       SUM(oi.Quantity) as UnitsSold
FROM Categories c
INNER JOIN Medicines m ON c.CategoryId = m.CategoryId
INNER JOIN OrderItems oi ON m.MedicineId = oi.MedicineId
GROUP BY c.Name ORDER BY Revenue DESC;
```

---

**🎯 This comprehensive SQL package provides all the queries needed to run the complete MediEase pharmacy management system with full functionality including e-commerce, inventory management, prescription processing, and business analytics.**
