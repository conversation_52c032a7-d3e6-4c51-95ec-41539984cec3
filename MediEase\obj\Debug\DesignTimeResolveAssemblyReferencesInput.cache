   .winmd.dll.exe (   (C:\Users\<USER>\Desktop\Project\Web.config/C:\Users\<USER>\Desktop\Project\DatabaseTest.aspx*C:\Users\<USER>\Desktop\Project\Default.aspx(C:\Users\<USER>\Desktop\Project\Login.aspx+C:\Users\<USER>\Desktop\Project\Register.aspx'C:\Users\<USER>\Desktop\Project\Test.aspx,C:\Users\<USER>\Desktop\Project\Medicines.aspx(C:\Users\<USER>\Desktop\Project\About.aspx5C:\Users\<USER>\Desktop\Project\Customer\Dashboard.aspx)C:\Users\<USER>\Desktop\Project\Site.Master)C:\Users\<USER>\Desktop\Project\Global.asax.C:\Users\<USER>\Desktop\Project\Content\Site.css-C:\Users\<USER>\Desktop\Project\Scripts\Site.js7C:\Users\<USER>\Desktop\Project\Handlers\ChatHandler.ashx-C:\Users\<USER>\Desktop\Project\packages.config.C:\Users\<USER>\Desktop\Project\Web.Debug.config0C:\Users\<USER>\Desktop\Project\Web.Release.configZC:\Users\<USER>\Desktop\Project\packages\BCrypt.Net-Next.4.0.3\lib\net48\BCrypt.Net-Next.dllZC:\Users\<USER>\Desktop\Project\packages\EntityFramework.6.5.1\lib\net45\EntityFramework.dlldC:\Users\<USER>\Desktop\Project\packages\EntityFramework.6.5.1\lib\net45\EntityFramework.SqlServer.dllgC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Microsoft.CSharp.dllvC:\Users\<USER>\Desktop\Project\packages\Microsoft.Web.Infrastructure.*******\lib\net40\Microsoft.Web.Infrastructure.dll_C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\mscorlib.dll[C:\Users\<USER>\Desktop\Project\packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.dll|C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.ComponentModel.DataAnnotations.dllkC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Configuration.dllbC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Core.dlltC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Data.DataSetExtensions.dllbC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Data.dll]C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.dlleC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Drawing.dllpC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.EnterpriseServices.dlluC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Web.ApplicationServices.dllaC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Web.dllmC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Web.DynamicData.dllhC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Web.Entity.dlllC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Web.Extensions.dlljC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Web.Services.dllaC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Xml.dllfC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Xml.Linq.dll       SC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\   Full                 {CandidateAssemblyFiles}{HintPathFromItem}{TargetFrameworkDirectory}B{Registry:Software\Microsoft\.NETFramework,v4.8,AssemblyFoldersEx}
{RawFileName}+C:\Users\<USER>\Desktop\Project\MediEase\bin\     B{Registry:Software\Microsoft\.NETFramework,v4.8,AssemblyFoldersEx}ZC:\Users\<USER>\Desktop\Project\MediEase\obj\Debug\DesignTimeResolveAssemblyReferences.cache   SC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\[C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\.NETFramework,Version=v4.8.NET Framework 4.8v4.8msil
v4.0.30319         