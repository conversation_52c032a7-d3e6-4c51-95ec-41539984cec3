-- =============================================
-- MediEase Database Schema Creation Script
-- Complete database structure for all features
-- =============================================

USE master;
GO

-- Create MediEase Database
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'MediEase')
BEGIN
    CREATE DATABASE MediEase;
END
GO

USE MediEase;
GO

-- =============================================
-- 1. USERS AND AUTHENTICATION TABLES
-- =============================================

-- Users Table (Core user management)
CREATE TABLE Users (
    UserId INT IDENTITY(1,1) PRIMARY KEY,
    Email NVARCHAR(100) NOT NULL UNIQUE,
    PasswordHash NVARCHAR(255) NOT NULL,
    FirstName NVARCHAR(50) NOT NULL,
    LastName NVARCHAR(50) NOT NULL,
    PhoneNumber NVARCHAR(20),
    Role NVARCHAR(20) NOT NULL DEFAULT 'Customer', -- Admin, Pharmacist, Customer
    IsActive BIT NOT NULL DEFAULT 1,
    IsEmailVerified BIT NOT NULL DEFAULT 0,
    EmailVerificationToken NVARCHAR(255),
    PasswordResetToken NVARCHAR(255),
    PasswordResetExpiry DATETIME,
    LastLoginDate DATETIME,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
    ModifiedDate DATETIME,
    
    -- Address Information
    Address NVARCHAR(500),
    City NVARCHAR(100),
    State NVARCHAR(100),
    PostalCode NVARCHAR(20),
    Country NVARCHAR(100) DEFAULT 'USA',
    
    -- Customer Specific Fields
    DateOfBirth DATE,
    Gender NVARCHAR(10),
    LoyaltyPoints INT DEFAULT 0,
    PreferredLanguage NVARCHAR(10) DEFAULT 'en',
    
    -- Pharmacist Specific Fields
    LicenseNumber NVARCHAR(50),
    LicenseExpiry DATE,
    Specialization NVARCHAR(100),
    
    -- Profile Information
    ProfilePicture NVARCHAR(255),
    Bio NVARCHAR(1000),
    
    INDEX IX_Users_Email (Email),
    INDEX IX_Users_Role (Role),
    INDEX IX_Users_IsActive (IsActive)
);

-- User Sessions Table
CREATE TABLE UserSessions (
    SessionId INT IDENTITY(1,1) PRIMARY KEY,
    UserId INT NOT NULL,
    SessionToken NVARCHAR(255) NOT NULL UNIQUE,
    IPAddress NVARCHAR(45),
    UserAgent NVARCHAR(500),
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
    ExpiryDate DATETIME NOT NULL,
    IsActive BIT NOT NULL DEFAULT 1,
    
    FOREIGN KEY (UserId) REFERENCES Users(UserId) ON DELETE CASCADE
);

-- User Preferences Table
CREATE TABLE UserPreferences (
    PreferenceId INT IDENTITY(1,1) PRIMARY KEY,
    UserId INT NOT NULL,
    NotificationEmail BIT DEFAULT 1,
    NotificationSMS BIT DEFAULT 0,
    NotificationPush BIT DEFAULT 1,
    Theme NVARCHAR(20) DEFAULT 'light',
    Language NVARCHAR(10) DEFAULT 'en',
    TimeZone NVARCHAR(50) DEFAULT 'UTC',
    
    FOREIGN KEY (UserId) REFERENCES Users(UserId) ON DELETE CASCADE
);

-- =============================================
-- 2. MEDICINE AND INVENTORY TABLES
-- =============================================

-- Medicine Categories
CREATE TABLE Categories (
    CategoryId INT IDENTITY(1,1) PRIMARY KEY,
    Name NVARCHAR(100) NOT NULL UNIQUE,
    Description NVARCHAR(500),
    ImageUrl NVARCHAR(255),
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE()
);

-- Medicine Brands
CREATE TABLE Brands (
    BrandId INT IDENTITY(1,1) PRIMARY KEY,
    Name NVARCHAR(100) NOT NULL UNIQUE,
    Description NVARCHAR(500),
    LogoUrl NVARCHAR(255),
    Website NVARCHAR(255),
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE()
);

-- Medicines Table (Core product catalog)
CREATE TABLE Medicines (
    MedicineId INT IDENTITY(1,1) PRIMARY KEY,
    Name NVARCHAR(200) NOT NULL,
    GenericName NVARCHAR(200),
    Description NVARCHAR(2000),
    CategoryId INT NOT NULL,
    BrandId INT,
    
    -- Pricing
    Price DECIMAL(10,2) NOT NULL,
    DiscountPercentage DECIMAL(5,2) DEFAULT 0,
    CostPrice DECIMAL(10,2),
    
    -- Inventory
    StockQuantity INT NOT NULL DEFAULT 0,
    MinStockLevel INT DEFAULT 10,
    MaxStockLevel INT DEFAULT 1000,
    ReorderLevel INT DEFAULT 20,
    
    -- Medicine Details
    PrescriptionRequired BIT NOT NULL DEFAULT 0,
    Dosage NVARCHAR(100),
    DosageForm NVARCHAR(50), -- Tablet, Capsule, Syrup, etc.
    Strength NVARCHAR(50),
    PackSize INT DEFAULT 1,
    
    -- Regulatory Information
    NDCNumber NVARCHAR(50), -- National Drug Code
    LotNumber NVARCHAR(50),
    ExpiryDate DATE,
    ManufactureDate DATE,
    
    -- Marketing
    IsFeatured BIT NOT NULL DEFAULT 0,
    IsNewArrival BIT NOT NULL DEFAULT 0,
    IsBestSeller BIT NOT NULL DEFAULT 0,
    
    -- Ratings and Reviews
    AverageRating DECIMAL(3,2) DEFAULT 0,
    ReviewCount INT DEFAULT 0,
    PurchaseCount INT DEFAULT 0,
    
    -- SEO and Images
    ImageUrl NVARCHAR(255),
    ThumbnailUrl NVARCHAR(255),
    SEOTitle NVARCHAR(200),
    SEODescription NVARCHAR(500),
    SEOKeywords NVARCHAR(500),
    
    -- Status
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
    ModifiedDate DATETIME,
    CreatedBy INT,
    ModifiedBy INT,
    
    FOREIGN KEY (CategoryId) REFERENCES Categories(CategoryId),
    FOREIGN KEY (BrandId) REFERENCES Brands(BrandId),
    FOREIGN KEY (CreatedBy) REFERENCES Users(UserId),
    FOREIGN KEY (ModifiedBy) REFERENCES Users(UserId),
    
    INDEX IX_Medicines_Category (CategoryId),
    INDEX IX_Medicines_Brand (BrandId),
    INDEX IX_Medicines_Price (Price),
    INDEX IX_Medicines_Stock (StockQuantity),
    INDEX IX_Medicines_Featured (IsFeatured),
    INDEX IX_Medicines_Active (IsActive)
);

-- Medicine Images Table
CREATE TABLE MedicineImages (
    ImageId INT IDENTITY(1,1) PRIMARY KEY,
    MedicineId INT NOT NULL,
    ImageUrl NVARCHAR(255) NOT NULL,
    AltText NVARCHAR(200),
    IsPrimary BIT NOT NULL DEFAULT 0,
    DisplayOrder INT DEFAULT 0,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
    
    FOREIGN KEY (MedicineId) REFERENCES Medicines(MedicineId) ON DELETE CASCADE
);

-- Medicine Interactions Table
CREATE TABLE MedicineInteractions (
    InteractionId INT IDENTITY(1,1) PRIMARY KEY,
    MedicineId1 INT NOT NULL,
    MedicineId2 INT NOT NULL,
    InteractionType NVARCHAR(50), -- Major, Moderate, Minor
    Description NVARCHAR(1000),
    Severity NVARCHAR(20), -- High, Medium, Low
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
    
    FOREIGN KEY (MedicineId1) REFERENCES Medicines(MedicineId),
    FOREIGN KEY (MedicineId2) REFERENCES Medicines(MedicineId)
);

-- Stock Movements Table (Inventory tracking)
CREATE TABLE StockMovements (
    MovementId INT IDENTITY(1,1) PRIMARY KEY,
    MedicineId INT NOT NULL,
    MovementType NVARCHAR(20) NOT NULL, -- IN, OUT, ADJUSTMENT, EXPIRED
    Quantity INT NOT NULL,
    PreviousStock INT NOT NULL,
    NewStock INT NOT NULL,
    UnitCost DECIMAL(10,2),
    TotalCost DECIMAL(10,2),
    Reference NVARCHAR(100), -- Order number, supplier invoice, etc.
    Notes NVARCHAR(500),
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
    CreatedBy INT,
    
    FOREIGN KEY (MedicineId) REFERENCES Medicines(MedicineId),
    FOREIGN KEY (CreatedBy) REFERENCES Users(UserId),
    
    INDEX IX_StockMovements_Medicine (MedicineId),
    INDEX IX_StockMovements_Type (MovementType),
    INDEX IX_StockMovements_Date (CreatedDate)
);

-- =============================================
-- 3. ORDERS AND TRANSACTIONS TABLES
-- =============================================

-- Orders Table
CREATE TABLE Orders (
    OrderId INT IDENTITY(1,1) PRIMARY KEY,
    OrderNumber NVARCHAR(50) NOT NULL UNIQUE,
    CustomerId INT NOT NULL,
    
    -- Order Status
    Status NVARCHAR(20) NOT NULL DEFAULT 'Pending', -- Pending, Processing, Verified, Packed, Shipped, Delivered, Cancelled
    
    -- Dates
    OrderDate DATETIME NOT NULL DEFAULT GETDATE(),
    ExpectedDeliveryDate DATETIME,
    ActualDeliveryDate DATETIME,
    
    -- Amounts
    Subtotal DECIMAL(10,2) NOT NULL,
    TaxAmount DECIMAL(10,2) DEFAULT 0,
    ShippingCost DECIMAL(10,2) DEFAULT 0,
    DiscountAmount DECIMAL(10,2) DEFAULT 0,
    TotalAmount DECIMAL(10,2) NOT NULL,
    
    -- Payment Information
    PaymentMethod NVARCHAR(50), -- CreditCard, DebitCard, PayPal, CashOnDelivery
    PaymentStatus NVARCHAR(20) DEFAULT 'Pending', -- Pending, Paid, Failed, Refunded
    PaymentReference NVARCHAR(100),
    PaymentTransactionId NVARCHAR(100),
    PaymentDate DATETIME,
    
    -- Invoice Information
    InvoiceGenerated BIT DEFAULT 0,
    InvoiceDate DATETIME,
    InvoiceNumber NVARCHAR(50),
    
    -- Shipping Information
    ShippingAddress NVARCHAR(500) NOT NULL,
    ShippingCity NVARCHAR(100),
    ShippingState NVARCHAR(100),
    ShippingPostalCode NVARCHAR(20),
    ShippingCountry NVARCHAR(100),
    ContactPhone NVARCHAR(20),
    ContactEmail NVARCHAR(100),
    
    -- Delivery Information
    TrackingNumber NVARCHAR(100),
    CourierService NVARCHAR(100),
    DeliveryInstructions NVARCHAR(500),
    
    -- Prescription Information
    RequiresPrescription BIT DEFAULT 0,
    PrescriptionVerified BIT DEFAULT 0,
    VerifiedBy INT,
    VerificationDate DATETIME,
    VerificationNotes NVARCHAR(500),
    
    -- Flags
    IsUrgent BIT DEFAULT 0,
    IsGift BIT DEFAULT 0,
    
    -- Audit
    Notes NVARCHAR(1000),
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
    ModifiedDate DATETIME,
    CreatedBy INT,
    ModifiedBy INT,
    
    FOREIGN KEY (CustomerId) REFERENCES Users(UserId),
    FOREIGN KEY (VerifiedBy) REFERENCES Users(UserId),
    FOREIGN KEY (CreatedBy) REFERENCES Users(UserId),
    FOREIGN KEY (ModifiedBy) REFERENCES Users(UserId),
    
    INDEX IX_Orders_Customer (CustomerId),
    INDEX IX_Orders_Status (Status),
    INDEX IX_Orders_Date (OrderDate),
    INDEX IX_Orders_PaymentStatus (PaymentStatus)
);

-- Order Items Table
CREATE TABLE OrderItems (
    OrderItemId INT IDENTITY(1,1) PRIMARY KEY,
    OrderId INT NOT NULL,
    MedicineId INT NOT NULL,
    Quantity INT NOT NULL,
    UnitPrice DECIMAL(10,2) NOT NULL,
    DiscountAmount DECIMAL(10,2) DEFAULT 0,
    TotalPrice DECIMAL(10,2) NOT NULL,
    SpecialInstructions NVARCHAR(500),
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),

    FOREIGN KEY (OrderId) REFERENCES Orders(OrderId) ON DELETE CASCADE,
    FOREIGN KEY (MedicineId) REFERENCES Medicines(MedicineId),

    INDEX IX_OrderItems_Order (OrderId),
    INDEX IX_OrderItems_Medicine (MedicineId)
);

-- Shopping Cart Table
CREATE TABLE ShoppingCart (
    CartId INT IDENTITY(1,1) PRIMARY KEY,
    UserId INT NOT NULL,
    MedicineId INT NOT NULL,
    Quantity INT NOT NULL,
    AddedDate DATETIME NOT NULL DEFAULT GETDATE(),
    ModifiedDate DATETIME,

    FOREIGN KEY (UserId) REFERENCES Users(UserId) ON DELETE CASCADE,
    FOREIGN KEY (MedicineId) REFERENCES Medicines(MedicineId),

    UNIQUE (UserId, MedicineId),
    INDEX IX_ShoppingCart_User (UserId)
);

-- =============================================
-- 4. PRESCRIPTIONS AND MEDICAL TABLES
-- =============================================

-- Prescriptions Table
CREATE TABLE Prescriptions (
    PrescriptionId INT IDENTITY(1,1) PRIMARY KEY,
    UserId INT NOT NULL,
    PrescriptionNumber NVARCHAR(50) NOT NULL UNIQUE,

    -- Doctor Information
    DoctorName NVARCHAR(100) NOT NULL,
    DoctorLicense NVARCHAR(50),
    DoctorPhone NVARCHAR(20),
    DoctorEmail NVARCHAR(100),
    ClinicName NVARCHAR(200),
    ClinicAddress NVARCHAR(500),

    -- Prescription Details
    PrescriptionDate DATE NOT NULL,
    ExpiryDate DATE,
    Diagnosis NVARCHAR(500),
    Instructions NVARCHAR(1000),

    -- File Information
    OriginalFileName NVARCHAR(255),
    FilePath NVARCHAR(500),
    FileSize BIGINT,
    FileType NVARCHAR(50),

    -- AI Processing
    AIProcessed BIT DEFAULT 0,
    AIExtractedText NVARCHAR(MAX),
    AIConfidenceScore DECIMAL(5,2),
    AIProcessingDate DATETIME,

    -- Verification
    Status NVARCHAR(20) DEFAULT 'Pending', -- Pending, Verified, Rejected, Expired
    VerifiedBy INT,
    VerificationDate DATETIME,
    VerificationNotes NVARCHAR(1000),
    RejectionReason NVARCHAR(500),

    -- Audit
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
    ModifiedDate DATETIME,

    FOREIGN KEY (UserId) REFERENCES Users(UserId),
    FOREIGN KEY (VerifiedBy) REFERENCES Users(UserId),

    INDEX IX_Prescriptions_User (UserId),
    INDEX IX_Prescriptions_Status (Status),
    INDEX IX_Prescriptions_Date (PrescriptionDate)
);

-- Prescription Items Table
CREATE TABLE PrescriptionItems (
    PrescriptionItemId INT IDENTITY(1,1) PRIMARY KEY,
    PrescriptionId INT NOT NULL,
    MedicineId INT,
    MedicineName NVARCHAR(200) NOT NULL,
    Dosage NVARCHAR(100),
    Frequency NVARCHAR(100),
    Duration NVARCHAR(100),
    Quantity INT,
    Instructions NVARCHAR(500),
    IsDispensed BIT DEFAULT 0,
    DispensedDate DATETIME,
    DispensedBy INT,

    FOREIGN KEY (PrescriptionId) REFERENCES Prescriptions(PrescriptionId) ON DELETE CASCADE,
    FOREIGN KEY (MedicineId) REFERENCES Medicines(MedicineId),
    FOREIGN KEY (DispensedBy) REFERENCES Users(UserId)
);

-- Family Profiles Table
CREATE TABLE FamilyProfiles (
    FamilyProfileId INT IDENTITY(1,1) PRIMARY KEY,
    UserId INT NOT NULL, -- Primary account holder
    MemberName NVARCHAR(100) NOT NULL,
    Relationship NVARCHAR(50), -- Self, Spouse, Child, Parent, etc.
    DateOfBirth DATE,
    Gender NVARCHAR(10),
    BloodGroup NVARCHAR(10),
    Allergies NVARCHAR(1000),
    MedicalConditions NVARCHAR(1000),
    EmergencyContact NVARCHAR(100),
    EmergencyPhone NVARCHAR(20),
    IsActive BIT DEFAULT 1,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),

    FOREIGN KEY (UserId) REFERENCES Users(UserId) ON DELETE CASCADE,

    INDEX IX_FamilyProfiles_User (UserId)
);

-- Health Reminders Table
CREATE TABLE HealthReminders (
    ReminderId INT IDENTITY(1,1) PRIMARY KEY,
    UserId INT NOT NULL,
    FamilyProfileId INT,
    Title NVARCHAR(200) NOT NULL,
    Description NVARCHAR(1000),
    ReminderType NVARCHAR(50), -- Medication, Appointment, Checkup, Refill
    NextReminderDate DATETIME NOT NULL,
    Frequency NVARCHAR(50), -- Daily, Weekly, Monthly, One-time
    FrequencyValue INT DEFAULT 1,
    EndDate DATETIME,
    IsActive BIT DEFAULT 1,
    IsCompleted BIT DEFAULT 0,
    CompletedDate DATETIME,
    SnoozeCount INT DEFAULT 0,
    LastSnoozedDate DATETIME,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),

    FOREIGN KEY (UserId) REFERENCES Users(UserId) ON DELETE CASCADE,
    FOREIGN KEY (FamilyProfileId) REFERENCES FamilyProfiles(FamilyProfileId),

    INDEX IX_HealthReminders_User (UserId),
    INDEX IX_HealthReminders_Date (NextReminderDate),
    INDEX IX_HealthReminders_Active (IsActive)
);

-- =============================================
-- 5. REVIEWS AND RATINGS TABLES
-- =============================================

-- Medicine Reviews Table
CREATE TABLE MedicineReviews (
    ReviewId INT IDENTITY(1,1) PRIMARY KEY,
    MedicineId INT NOT NULL,
    UserId INT NOT NULL,
    OrderId INT,
    Rating INT NOT NULL CHECK (Rating >= 1 AND Rating <= 5),
    Title NVARCHAR(200),
    ReviewText NVARCHAR(2000),
    IsVerifiedPurchase BIT DEFAULT 0,
    IsApproved BIT DEFAULT 0,
    ApprovedBy INT,
    ApprovedDate DATETIME,
    HelpfulVotes INT DEFAULT 0,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
    ModifiedDate DATETIME,

    FOREIGN KEY (MedicineId) REFERENCES Medicines(MedicineId),
    FOREIGN KEY (UserId) REFERENCES Users(UserId),
    FOREIGN KEY (OrderId) REFERENCES Orders(OrderId),
    FOREIGN KEY (ApprovedBy) REFERENCES Users(UserId),

    INDEX IX_MedicineReviews_Medicine (MedicineId),
    INDEX IX_MedicineReviews_User (UserId),
    INDEX IX_MedicineReviews_Rating (Rating)
);

-- Review Helpfulness Table
CREATE TABLE ReviewHelpfulness (
    HelpfulnessId INT IDENTITY(1,1) PRIMARY KEY,
    ReviewId INT NOT NULL,
    UserId INT NOT NULL,
    IsHelpful BIT NOT NULL,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),

    FOREIGN KEY (ReviewId) REFERENCES MedicineReviews(ReviewId) ON DELETE CASCADE,
    FOREIGN KEY (UserId) REFERENCES Users(UserId),

    UNIQUE (ReviewId, UserId)
);

-- =============================================
-- 6. LOYALTY AND OFFERS TABLES
-- =============================================

-- Loyalty Transactions Table
CREATE TABLE LoyaltyTransactions (
    TransactionId INT IDENTITY(1,1) PRIMARY KEY,
    UserId INT NOT NULL,
    OrderId INT,
    TransactionType NVARCHAR(20) NOT NULL, -- EARNED, REDEEMED, EXPIRED, BONUS
    Points INT NOT NULL,
    Description NVARCHAR(500),
    ExpiryDate DATETIME,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),

    FOREIGN KEY (UserId) REFERENCES Users(UserId),
    FOREIGN KEY (OrderId) REFERENCES Orders(OrderId),

    INDEX IX_LoyaltyTransactions_User (UserId),
    INDEX IX_LoyaltyTransactions_Type (TransactionType)
);

-- Offers and Coupons Table
CREATE TABLE Offers (
    OfferId INT IDENTITY(1,1) PRIMARY KEY,
    Code NVARCHAR(50) NOT NULL UNIQUE,
    Title NVARCHAR(200) NOT NULL,
    Description NVARCHAR(1000),
    OfferType NVARCHAR(20) NOT NULL, -- PERCENTAGE, FIXED_AMOUNT, FREE_SHIPPING
    DiscountValue DECIMAL(10,2) NOT NULL,
    MinOrderAmount DECIMAL(10,2) DEFAULT 0,
    MaxDiscountAmount DECIMAL(10,2),
    UsageLimit INT,
    UsageCount INT DEFAULT 0,
    UserUsageLimit INT DEFAULT 1,
    StartDate DATETIME NOT NULL,
    EndDate DATETIME NOT NULL,
    IsActive BIT DEFAULT 1,
    ApplicableCategories NVARCHAR(500), -- Comma-separated category IDs
    ApplicableMedicines NVARCHAR(500), -- Comma-separated medicine IDs
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
    CreatedBy INT,

    FOREIGN KEY (CreatedBy) REFERENCES Users(UserId),

    INDEX IX_Offers_Code (Code),
    INDEX IX_Offers_Active (IsActive),
    INDEX IX_Offers_Dates (StartDate, EndDate)
);

-- User Offer Usage Table
CREATE TABLE UserOfferUsage (
    UsageId INT IDENTITY(1,1) PRIMARY KEY,
    UserId INT NOT NULL,
    OfferId INT NOT NULL,
    OrderId INT NOT NULL,
    DiscountAmount DECIMAL(10,2) NOT NULL,
    UsedDate DATETIME NOT NULL DEFAULT GETDATE(),

    FOREIGN KEY (UserId) REFERENCES Users(UserId),
    FOREIGN KEY (OfferId) REFERENCES Offers(OfferId),
    FOREIGN KEY (OrderId) REFERENCES Orders(OrderId),

    INDEX IX_UserOfferUsage_User (UserId),
    INDEX IX_UserOfferUsage_Offer (OfferId)
);

-- =============================================
-- 7. NOTIFICATIONS AND COMMUNICATIONS TABLES
-- =============================================

-- Notifications Table
CREATE TABLE Notifications (
    NotificationId INT IDENTITY(1,1) PRIMARY KEY,
    UserId INT NOT NULL,
    Title NVARCHAR(200) NOT NULL,
    Message NVARCHAR(1000) NOT NULL,
    NotificationType NVARCHAR(50), -- ORDER_UPDATE, REMINDER, PROMOTION, SYSTEM
    RelatedEntityType NVARCHAR(50), -- Order, Prescription, Medicine, etc.
    RelatedEntityId INT,
    IsRead BIT DEFAULT 0,
    ReadDate DATETIME,
    Priority NVARCHAR(20) DEFAULT 'Normal', -- Low, Normal, High, Urgent
    ExpiryDate DATETIME,
    ActionUrl NVARCHAR(500),
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),

    FOREIGN KEY (UserId) REFERENCES Users(UserId) ON DELETE CASCADE,

    INDEX IX_Notifications_User (UserId),
    INDEX IX_Notifications_Read (IsRead),
    INDEX IX_Notifications_Type (NotificationType)
);

-- Email Queue Table
CREATE TABLE EmailQueue (
    EmailId INT IDENTITY(1,1) PRIMARY KEY,
    ToEmail NVARCHAR(100) NOT NULL,
    ToName NVARCHAR(100),
    Subject NVARCHAR(200) NOT NULL,
    Body NVARCHAR(MAX) NOT NULL,
    IsHtml BIT DEFAULT 1,
    Priority INT DEFAULT 3, -- 1=High, 3=Normal, 5=Low
    Status NVARCHAR(20) DEFAULT 'Pending', -- Pending, Sent, Failed
    AttemptCount INT DEFAULT 0,
    MaxAttempts INT DEFAULT 3,
    ErrorMessage NVARCHAR(1000),
    ScheduledDate DATETIME,
    SentDate DATETIME,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),

    INDEX IX_EmailQueue_Status (Status),
    INDEX IX_EmailQueue_Scheduled (ScheduledDate)
);

-- SMS Queue Table
CREATE TABLE SMSQueue (
    SMSId INT IDENTITY(1,1) PRIMARY KEY,
    ToPhoneNumber NVARCHAR(20) NOT NULL,
    Message NVARCHAR(500) NOT NULL,
    Status NVARCHAR(20) DEFAULT 'Pending', -- Pending, Sent, Failed
    AttemptCount INT DEFAULT 0,
    MaxAttempts INT DEFAULT 3,
    ErrorMessage NVARCHAR(1000),
    ScheduledDate DATETIME,
    SentDate DATETIME,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),

    INDEX IX_SMSQueue_Status (Status),
    INDEX IX_SMSQueue_Scheduled (ScheduledDate)
);

-- =============================================
-- 8. AI AND CHATBOT TABLES
-- =============================================

-- AI Chat Sessions Table
CREATE TABLE ChatSessions (
    SessionId INT IDENTITY(1,1) PRIMARY KEY,
    UserId INT,
    SessionToken NVARCHAR(255) NOT NULL UNIQUE,
    StartDate DATETIME NOT NULL DEFAULT GETDATE(),
    EndDate DATETIME,
    IsActive BIT DEFAULT 1,
    UserAgent NVARCHAR(500),
    IPAddress NVARCHAR(45),

    FOREIGN KEY (UserId) REFERENCES Users(UserId),

    INDEX IX_ChatSessions_User (UserId),
    INDEX IX_ChatSessions_Token (SessionToken)
);

-- AI Chat Messages Table
CREATE TABLE ChatMessages (
    MessageId INT IDENTITY(1,1) PRIMARY KEY,
    SessionId INT NOT NULL,
    MessageType NVARCHAR(20) NOT NULL, -- USER, AI, SYSTEM
    Message NVARCHAR(MAX) NOT NULL,
    Timestamp DATETIME NOT NULL DEFAULT GETDATE(),

    -- AI Response Metadata
    AIModel NVARCHAR(100),
    ResponseTime INT, -- milliseconds
    TokensUsed INT,
    ConfidenceScore DECIMAL(5,2),

    FOREIGN KEY (SessionId) REFERENCES ChatSessions(SessionId) ON DELETE CASCADE,

    INDEX IX_ChatMessages_Session (SessionId),
    INDEX IX_ChatMessages_Type (MessageType),
    INDEX IX_ChatMessages_Timestamp (Timestamp)
);

-- AI Recommendations Table
CREATE TABLE AIRecommendations (
    RecommendationId INT IDENTITY(1,1) PRIMARY KEY,
    UserId INT,
    SessionId INT,
    RecommendationType NVARCHAR(50), -- MEDICINE, ALTERNATIVE, INTERACTION_WARNING
    MedicineId INT,
    RecommendationText NVARCHAR(2000),
    ConfidenceScore DECIMAL(5,2),
    Reasoning NVARCHAR(1000),
    IsAccepted BIT,
    UserFeedback NVARCHAR(500),
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),

    FOREIGN KEY (UserId) REFERENCES Users(UserId),
    FOREIGN KEY (SessionId) REFERENCES ChatSessions(SessionId),
    FOREIGN KEY (MedicineId) REFERENCES Medicines(MedicineId),

    INDEX IX_AIRecommendations_User (UserId),
    INDEX IX_AIRecommendations_Type (RecommendationType)
);

-- =============================================
-- 9. SYSTEM MANAGEMENT TABLES
-- =============================================

-- System Settings Table
CREATE TABLE SystemSettings (
    SettingId INT IDENTITY(1,1) PRIMARY KEY,
    SettingKey NVARCHAR(100) NOT NULL UNIQUE,
    SettingValue NVARCHAR(2000),
    Description NVARCHAR(500),
    DataType NVARCHAR(20) DEFAULT 'String', -- String, Integer, Boolean, Decimal
    Category NVARCHAR(50),
    IsEditable BIT DEFAULT 1,
    ModifiedDate DATETIME,
    ModifiedBy INT,

    FOREIGN KEY (ModifiedBy) REFERENCES Users(UserId),

    INDEX IX_SystemSettings_Key (SettingKey),
    INDEX IX_SystemSettings_Category (Category)
);

-- Audit Logs Table
CREATE TABLE AuditLogs (
    LogId INT IDENTITY(1,1) PRIMARY KEY,
    UserId INT,
    Action NVARCHAR(100) NOT NULL,
    EntityType NVARCHAR(50),
    EntityId INT,
    OldValues NVARCHAR(MAX),
    NewValues NVARCHAR(MAX),
    IPAddress NVARCHAR(45),
    UserAgent NVARCHAR(500),
    Timestamp DATETIME NOT NULL DEFAULT GETDATE(),

    FOREIGN KEY (UserId) REFERENCES Users(UserId),

    INDEX IX_AuditLogs_User (UserId),
    INDEX IX_AuditLogs_Action (Action),
    INDEX IX_AuditLogs_Entity (EntityType, EntityId),
    INDEX IX_AuditLogs_Timestamp (Timestamp)
);

-- Error Logs Table
CREATE TABLE ErrorLogs (
    ErrorId INT IDENTITY(1,1) PRIMARY KEY,
    ErrorMessage NVARCHAR(2000) NOT NULL,
    StackTrace NVARCHAR(MAX),
    Source NVARCHAR(200),
    UserId INT,
    RequestUrl NVARCHAR(500),
    HttpMethod NVARCHAR(10),
    IPAddress NVARCHAR(45),
    UserAgent NVARCHAR(500),
    Severity NVARCHAR(20) DEFAULT 'Error', -- Info, Warning, Error, Critical
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),

    FOREIGN KEY (UserId) REFERENCES Users(UserId),

    INDEX IX_ErrorLogs_Severity (Severity),
    INDEX IX_ErrorLogs_Date (CreatedDate),
    INDEX IX_ErrorLogs_User (UserId)
);

-- File Uploads Table
CREATE TABLE FileUploads (
    FileId INT IDENTITY(1,1) PRIMARY KEY,
    UserId INT,
    OriginalFileName NVARCHAR(255) NOT NULL,
    StoredFileName NVARCHAR(255) NOT NULL,
    FilePath NVARCHAR(500) NOT NULL,
    FileSize BIGINT NOT NULL,
    ContentType NVARCHAR(100),
    FileCategory NVARCHAR(50), -- PRESCRIPTION, PROFILE_PICTURE, MEDICINE_IMAGE
    RelatedEntityType NVARCHAR(50),
    RelatedEntityId INT,
    IsActive BIT DEFAULT 1,
    UploadDate DATETIME NOT NULL DEFAULT GETDATE(),

    FOREIGN KEY (UserId) REFERENCES Users(UserId),

    INDEX IX_FileUploads_User (UserId),
    INDEX IX_FileUploads_Category (FileCategory),
    INDEX IX_FileUploads_Entity (RelatedEntityType, RelatedEntityId)
);

-- =============================================
-- 10. REPORTS AND ANALYTICS TABLES
-- =============================================

-- Daily Sales Summary Table
CREATE TABLE DailySalesSummary (
    SummaryId INT IDENTITY(1,1) PRIMARY KEY,
    SummaryDate DATE NOT NULL UNIQUE,
    TotalOrders INT DEFAULT 0,
    TotalRevenue DECIMAL(12,2) DEFAULT 0,
    TotalCustomers INT DEFAULT 0,
    NewCustomers INT DEFAULT 0,
    PrescriptionOrders INT DEFAULT 0,
    OnlineOrders INT DEFAULT 0,
    CashOrders INT DEFAULT 0,
    AverageOrderValue DECIMAL(10,2) DEFAULT 0,
    TopSellingMedicineId INT,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),

    FOREIGN KEY (TopSellingMedicineId) REFERENCES Medicines(MedicineId),

    INDEX IX_DailySalesSummary_Date (SummaryDate)
);

-- User Activity Logs Table
CREATE TABLE UserActivityLogs (
    ActivityId INT IDENTITY(1,1) PRIMARY KEY,
    UserId INT,
    ActivityType NVARCHAR(50), -- LOGIN, LOGOUT, SEARCH, VIEW_PRODUCT, ADD_TO_CART, etc.
    Description NVARCHAR(500),
    IPAddress NVARCHAR(45),
    UserAgent NVARCHAR(500),
    SessionId NVARCHAR(255),
    Duration INT, -- seconds
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),

    FOREIGN KEY (UserId) REFERENCES Users(UserId),

    INDEX IX_UserActivityLogs_User (UserId),
    INDEX IX_UserActivityLogs_Type (ActivityType),
    INDEX IX_UserActivityLogs_Date (CreatedDate)
);

-- =============================================
-- 11. INITIAL DATA SETUP
-- =============================================

-- Insert Default Categories
INSERT INTO Categories (Name, Description, IsActive) VALUES
('Pain Relief', 'Medications for pain management and relief', 1),
('Antibiotics', 'Prescription antibiotics for bacterial infections', 1),
('Vitamins & Supplements', 'Nutritional supplements and vitamins', 1),
('Cold & Flu', 'Medications for cold and flu symptoms', 1),
('Digestive Health', 'Medications for digestive and stomach issues', 1),
('Heart & Blood Pressure', 'Cardiovascular medications', 1),
('Diabetes Care', 'Medications and supplies for diabetes management', 1),
('Skin Care', 'Topical medications and skin treatments', 1),
('Eye Care', 'Eye drops and vision care products', 1),
('First Aid', 'Emergency and first aid supplies', 1);

-- Insert Default Brands
INSERT INTO Brands (Name, Description, IsActive) VALUES
('Generic', 'Generic pharmaceutical products', 1),
('Pfizer', 'Leading pharmaceutical company', 1),
('Johnson & Johnson', 'Healthcare and pharmaceutical products', 1),
('Bayer', 'German pharmaceutical and life sciences company', 1),
('GSK', 'GlaxoSmithKline pharmaceutical products', 1),
('Novartis', 'Swiss multinational pharmaceutical company', 1),
('Merck', 'American multinational pharmaceutical company', 1),
('Abbott', 'Healthcare and pharmaceutical products', 1),
('Roche', 'Swiss multinational healthcare company', 1),
('Sanofi', 'French multinational pharmaceutical company', 1);

-- Insert Sample Medicines
INSERT INTO Medicines (Name, GenericName, Description, CategoryId, BrandId, Price, DiscountPercentage, StockQuantity, PrescriptionRequired, Dosage, DosageForm, Strength, IsFeatured, IsActive) VALUES
('Tylenol Extra Strength', 'Acetaminophen', 'Fast-acting pain relief for headaches, muscle aches, and fever', 1, 3, 12.99, 10, 150, 0, '500mg', 'Tablet', '500mg', 1, 1),
('Advil Liqui-Gels', 'Ibuprofen', 'Fast pain relief in liquid-filled capsules', 1, 3, 15.49, 15, 200, 0, '200mg', 'Capsule', '200mg', 1, 1),
('Amoxicillin', 'Amoxicillin', 'Broad-spectrum antibiotic for bacterial infections', 2, 1, 25.99, 0, 75, 1, '500mg', 'Capsule', '500mg', 0, 1),
('Vitamin D3', 'Cholecalciferol', 'Essential vitamin for bone health and immune support', 3, 1, 18.99, 20, 300, 0, '1000 IU', 'Tablet', '1000 IU', 1, 1),
('Robitussin DM', 'Dextromethorphan', 'Cough suppressant and expectorant', 4, 3, 11.99, 5, 120, 0, '15mg/5ml', 'Syrup', '15mg/5ml', 0, 1),
('Pepto-Bismol', 'Bismuth Subsalicylate', 'Relief from upset stomach, nausea, and diarrhea', 5, 3, 9.99, 0, 180, 0, '262mg', 'Tablet', '262mg', 0, 1),
('Lisinopril', 'Lisinopril', 'ACE inhibitor for high blood pressure', 6, 1, 32.50, 0, 90, 1, '10mg', 'Tablet', '10mg', 0, 1),
('Metformin', 'Metformin HCl', 'Type 2 diabetes medication', 7, 1, 28.75, 0, 100, 1, '500mg', 'Tablet', '500mg', 0, 1),
('Hydrocortisone Cream', 'Hydrocortisone', 'Topical anti-inflammatory for skin irritation', 8, 1, 7.99, 0, 250, 0, '1%', 'Cream', '1%', 0, 1),
('Artificial Tears', 'Polyethylene Glycol', 'Lubricating eye drops for dry eyes', 9, 1, 6.99, 10, 200, 0, '0.4%', 'Drops', '0.4%', 0, 1);

-- Insert System Settings
INSERT INTO SystemSettings (SettingKey, SettingValue, Description, Category, IsEditable) VALUES
('SiteName', 'MediEase', 'Name of the pharmacy system', 'General', 1),
('SiteEmail', '<EMAIL>', 'Primary contact email', 'General', 1),
('SitePhone', '******-0123', 'Primary contact phone number', 'General', 1),
('Currency', 'USD', 'Default currency for pricing', 'General', 1),
('TaxRate', '8.5', 'Default tax rate percentage', 'Financial', 1),
('ShippingCost', '5.99', 'Standard shipping cost', 'Financial', 1),
('FreeShippingThreshold', '50.00', 'Minimum order for free shipping', 'Financial', 1),
('LoyaltyPointsRate', '1', 'Points earned per dollar spent', 'Loyalty', 1),
('LoyaltyRedemptionRate', '100', 'Points needed for $1 discount', 'Loyalty', 1),
('MaxFileUploadSize', '5242880', 'Maximum file upload size in bytes (5MB)', 'System', 1),
('SessionTimeout', '30', 'Session timeout in minutes', 'Security', 1),
('PasswordMinLength', '8', 'Minimum password length', 'Security', 1),
('EnableEmailNotifications', 'true', 'Enable email notifications', 'Notifications', 1),
('EnableSMSNotifications', 'false', 'Enable SMS notifications', 'Notifications', 1),
('AIEnabled', 'true', 'Enable AI features', 'AI', 1),
('ChatbotEnabled', 'true', 'Enable chatbot functionality', 'AI', 1),
('PrescriptionVerificationRequired', 'true', 'Require prescription verification', 'Medical', 1),
('AutoApproveReviews', 'false', 'Automatically approve product reviews', 'Reviews', 1),
('MaintenanceMode', 'false', 'Enable maintenance mode', 'System', 1),
('BackupFrequency', 'Daily', 'Database backup frequency', 'System', 1);

-- Insert Initial Admin User (Change credentials before production!)
INSERT INTO Users (Email, PasswordHash, FirstName, LastName, PhoneNumber, Role, IsActive, IsEmailVerified, Address, City, State, PostalCode, Country) VALUES
('<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'System', 'Administrator', '******-0001', 'Admin', 1, 1, '123 Admin Street', 'Admin City', 'CA', '12345', 'USA');

-- =============================================
-- 12. VIEWS FOR REPORTING AND ANALYTICS
-- =============================================

-- View: Order Summary with Customer Details
CREATE VIEW vw_OrderSummary AS
SELECT
    o.OrderId,
    o.OrderNumber,
    o.OrderDate,
    o.Status,
    o.TotalAmount,
    o.PaymentStatus,
    u.FirstName + ' ' + u.LastName AS CustomerName,
    u.Email AS CustomerEmail,
    u.PhoneNumber AS CustomerPhone,
    COUNT(oi.OrderItemId) AS ItemCount,
    o.RequiresPrescription,
    o.PrescriptionVerified
FROM Orders o
INNER JOIN Users u ON o.CustomerId = u.UserId
LEFT JOIN OrderItems oi ON o.OrderId = oi.OrderId
GROUP BY o.OrderId, o.OrderNumber, o.OrderDate, o.Status, o.TotalAmount, o.PaymentStatus,
         u.FirstName, u.LastName, u.Email, u.PhoneNumber, o.RequiresPrescription, o.PrescriptionVerified;

-- View: Medicine Inventory Status
CREATE VIEW vw_MedicineInventory AS
SELECT
    m.MedicineId,
    m.Name,
    m.GenericName,
    c.Name AS CategoryName,
    b.Name AS BrandName,
    m.StockQuantity,
    m.MinStockLevel,
    m.ReorderLevel,
    CASE
        WHEN m.StockQuantity <= 0 THEN 'Out of Stock'
        WHEN m.StockQuantity <= m.MinStockLevel THEN 'Low Stock'
        WHEN m.StockQuantity <= m.ReorderLevel THEN 'Reorder Required'
        ELSE 'In Stock'
    END AS StockStatus,
    m.Price,
    m.DiscountPercentage,
    m.Price * (1 - m.DiscountPercentage / 100) AS FinalPrice,
    m.AverageRating,
    m.ReviewCount,
    m.PurchaseCount,
    m.IsActive
FROM Medicines m
INNER JOIN Categories c ON m.CategoryId = c.CategoryId
LEFT JOIN Brands b ON m.BrandId = b.BrandId;

-- View: Customer Order History
CREATE VIEW vw_CustomerOrderHistory AS
SELECT
    u.UserId,
    u.FirstName + ' ' + u.LastName AS CustomerName,
    u.Email,
    COUNT(DISTINCT o.OrderId) AS TotalOrders,
    SUM(o.TotalAmount) AS TotalSpent,
    AVG(o.TotalAmount) AS AverageOrderValue,
    MAX(o.OrderDate) AS LastOrderDate,
    u.LoyaltyPoints,
    CASE
        WHEN COUNT(DISTINCT o.OrderId) >= 10 THEN 'VIP'
        WHEN COUNT(DISTINCT o.OrderId) >= 5 THEN 'Regular'
        ELSE 'New'
    END AS CustomerTier
FROM Users u
LEFT JOIN Orders o ON u.UserId = o.CustomerId AND o.Status != 'Cancelled'
WHERE u.Role = 'Customer'
GROUP BY u.UserId, u.FirstName, u.LastName, u.Email, u.LoyaltyPoints;

-- View: Daily Sales Report
CREATE VIEW vw_DailySalesReport AS
SELECT
    CAST(o.OrderDate AS DATE) AS SaleDate,
    COUNT(DISTINCT o.OrderId) AS TotalOrders,
    COUNT(DISTINCT o.CustomerId) AS UniqueCustomers,
    SUM(o.TotalAmount) AS TotalRevenue,
    AVG(o.TotalAmount) AS AverageOrderValue,
    SUM(CASE WHEN o.RequiresPrescription = 1 THEN 1 ELSE 0 END) AS PrescriptionOrders,
    SUM(CASE WHEN o.PaymentMethod = 'CashOnDelivery' THEN 1 ELSE 0 END) AS CODOrders,
    SUM(CASE WHEN o.PaymentMethod != 'CashOnDelivery' THEN 1 ELSE 0 END) AS OnlineOrders
FROM Orders o
WHERE o.Status NOT IN ('Cancelled')
GROUP BY CAST(o.OrderDate AS DATE);

-- =============================================
-- 13. STORED PROCEDURES
-- =============================================

-- Procedure: Update Medicine Stock
CREATE PROCEDURE sp_UpdateMedicineStock
    @MedicineId INT,
    @Quantity INT,
    @MovementType NVARCHAR(20),
    @Reference NVARCHAR(100) = NULL,
    @Notes NVARCHAR(500) = NULL,
    @UserId INT = NULL
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @CurrentStock INT;
    DECLARE @NewStock INT;

    -- Get current stock
    SELECT @CurrentStock = StockQuantity FROM Medicines WHERE MedicineId = @MedicineId;

    -- Calculate new stock
    IF @MovementType = 'IN'
        SET @NewStock = @CurrentStock + @Quantity;
    ELSE IF @MovementType = 'OUT'
        SET @NewStock = @CurrentStock - @Quantity;
    ELSE
        SET @NewStock = @Quantity; -- For ADJUSTMENT

    -- Ensure stock doesn't go negative
    IF @NewStock < 0
        SET @NewStock = 0;

    -- Update medicine stock
    UPDATE Medicines
    SET StockQuantity = @NewStock,
        ModifiedDate = GETDATE()
    WHERE MedicineId = @MedicineId;

    -- Record stock movement
    INSERT INTO StockMovements (MedicineId, MovementType, Quantity, PreviousStock, NewStock, Reference, Notes, CreatedBy)
    VALUES (@MedicineId, @MovementType, @Quantity, @CurrentStock, @NewStock, @Reference, @Notes, @UserId);
END;

-- Procedure: Process Order
CREATE PROCEDURE sp_ProcessOrder
    @OrderId INT,
    @NewStatus NVARCHAR(20),
    @UserId INT = NULL,
    @Notes NVARCHAR(1000) = NULL
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @CurrentStatus NVARCHAR(20);

    -- Get current status
    SELECT @CurrentStatus = Status FROM Orders WHERE OrderId = @OrderId;

    -- Update order status
    UPDATE Orders
    SET Status = @NewStatus,
        ModifiedDate = GETDATE(),
        ModifiedBy = @UserId,
        Notes = COALESCE(@Notes, Notes)
    WHERE OrderId = @OrderId;

    -- If order is being delivered, update delivery date
    IF @NewStatus = 'Delivered'
    BEGIN
        UPDATE Orders
        SET ActualDeliveryDate = GETDATE()
        WHERE OrderId = @OrderId;
    END

    -- Log the status change
    INSERT INTO AuditLogs (UserId, Action, EntityType, EntityId, OldValues, NewValues)
    VALUES (@UserId, 'Order Status Change', 'Order', @OrderId, @CurrentStatus, @NewStatus);
END;

GO

PRINT 'MediEase database schema created successfully!';
PRINT 'Remember to:';
PRINT '1. Change the default admin credentials';
PRINT '2. Update system settings for your environment';
PRINT '3. Configure proper backup procedures';
PRINT '4. Set up appropriate security measures';
