@echo off
echo =============================================
echo MediEase Database Functionality Tests
echo =============================================
echo.

echo This script will run comprehensive tests on MediEase.mdf
echo to verify all data store/retrieve functionality works correctly.
echo.

echo Prerequisites:
echo - SQL Server LocalDB installed
echo - MediEase.mdf database created and accessible
echo - SQL Server Management Studio (optional for viewing results)
echo.

pause

echo.
echo Starting database validation tests...
echo.

echo Connecting to MediEase database...
sqlcmd -S "(LocalDB)\MSSQLLocalDB" -d "MediEase" -Q "SELECT 'Database connection successful' as Status, DB_NAME() as DatabaseName, GETDATE() as TestTime"

if %errorlevel% neq 0 (
    echo.
    echo ERROR: Cannot connect to MediEase database
    echo Please ensure:
    echo 1. SQL Server LocalDB is running
    echo 2. MediEase.mdf database exists
    echo 3. Database is properly attached
    echo.
    pause
    exit /b 1
)

echo.
echo Database connection successful!
echo.
echo Running comprehensive functionality tests...
echo This may take a few minutes...
echo.

sqlcmd -S "(LocalDB)\MSSQLLocalDB" -d "MediEase" -i "MediEase_Database_Validation.sql" -o "Test_Results.txt"

if %errorlevel% equ 0 (
    echo.
    echo =============================================
    echo TESTS COMPLETED SUCCESSFULLY!
    echo =============================================
    echo.
    echo Test results have been saved to: Test_Results.txt
    echo.
    echo Summary of tests performed:
    echo ✓ Database structure validation
    echo ✓ User authentication functionality
    echo ✓ Medicine catalog operations
    echo ✓ Shopping cart functionality
    echo ✓ Order processing workflow
    echo ✓ Prescription management
    echo ✓ Family profiles management
    echo ✓ Health reminders system
    echo ✓ Loyalty program features
    echo ✓ Reviews and ratings system
    echo ✓ Inventory management
    echo ✓ Notifications system
    echo ✓ AI chatbot functionality
    echo ✓ Offers and promotions
    echo ✓ System settings management
    echo ✓ Reporting and analytics
    echo ✓ Data integrity constraints
    echo ✓ Performance and indexing
    echo.
    echo All core functionality has been validated!
    echo MediEase.mdf is ready for production use.
    echo.
    echo To view detailed results, open Test_Results.txt
    echo.
) else (
    echo.
    echo ERROR: Some tests failed
    echo Please check Test_Results.txt for details
    echo.
)

echo Would you like to view the test results now? (Y/N)
set /p choice=
if /i "%choice%"=="Y" (
    if exist "Test_Results.txt" (
        notepad "Test_Results.txt"
    ) else (
        echo Test results file not found.
    )
)

echo.
echo =============================================
echo Test execution complete.
echo =============================================
pause
