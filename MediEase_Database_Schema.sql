-- =============================================
-- MediEase Database Schema for Manual Creation
-- Complete table structure for MediEase.mdf
-- =============================================

-- 1. USERS TABLE
CREATE TABLE [Users] (
    [UserId] INT IDENTITY(1,1) PRIMARY KEY,
    [Email] NVARCHAR(100) NOT NULL UNIQUE,
    [PasswordHash] NVARCHAR(255) NOT NULL,
    [FirstName] NVARCHAR(50) NOT NULL,
    [LastName] NVARCHAR(50) NOT NULL,
    [PhoneNumber] NVARCHAR(20),
    [Role] NVARCHAR(20) NOT NULL DEFAULT 'Customer',
    [IsActive] BIT NOT NULL DEFAULT 1,
    [IsEmailVerified] BIT NOT NULL DEFAULT 0,
    [EmailVerificationToken] NVARCHAR(255),
    [PasswordResetToken] NVARCHAR(255),
    [PasswordResetExpiry] DATETIME,
    [LastLoginDate] DATETIME,
    [CreatedDate] DATETIME NOT NULL DEFAULT GETDATE(),
    [ModifiedDate] DATETIME,
    [Address] NVARCHAR(500),
    [City] NVARCHAR(100),
    [State] NVARCHAR(100),
    [PostalCode] NVARCHAR(20),
    [Country] NVARCHAR(100) DEFAULT 'USA',
    [DateOfBirth] DATE,
    [Gender] NVARCHAR(10),
    [LoyaltyPoints] INT DEFAULT 0,
    [PreferredLanguage] NVARCHAR(10) DEFAULT 'en',
    [LicenseNumber] NVARCHAR(50),
    [LicenseExpiry] DATE,
    [Specialization] NVARCHAR(100),
    [ProfilePicture] NVARCHAR(255),
    [Bio] NVARCHAR(1000)
);

-- 2. CATEGORIES TABLE
CREATE TABLE [Categories] (
    [CategoryId] INT IDENTITY(1,1) PRIMARY KEY,
    [Name] NVARCHAR(100) NOT NULL UNIQUE,
    [Description] NVARCHAR(500),
    [ImageUrl] NVARCHAR(255),
    [IsActive] BIT NOT NULL DEFAULT 1,
    [CreatedDate] DATETIME NOT NULL DEFAULT GETDATE()
);

-- 3. BRANDS TABLE
CREATE TABLE [Brands] (
    [BrandId] INT IDENTITY(1,1) PRIMARY KEY,
    [Name] NVARCHAR(100) NOT NULL UNIQUE,
    [Description] NVARCHAR(500),
    [LogoUrl] NVARCHAR(255),
    [Website] NVARCHAR(255),
    [IsActive] BIT NOT NULL DEFAULT 1,
    [CreatedDate] DATETIME NOT NULL DEFAULT GETDATE()
);

-- 4. MEDICINES TABLE
CREATE TABLE [Medicines] (
    [MedicineId] INT IDENTITY(1,1) PRIMARY KEY,
    [Name] NVARCHAR(200) NOT NULL,
    [GenericName] NVARCHAR(200),
    [Description] NVARCHAR(2000),
    [CategoryId] INT NOT NULL,
    [BrandId] INT,
    [Price] DECIMAL(10,2) NOT NULL,
    [DiscountPercentage] DECIMAL(5,2) DEFAULT 0,
    [CostPrice] DECIMAL(10,2),
    [StockQuantity] INT NOT NULL DEFAULT 0,
    [MinStockLevel] INT DEFAULT 10,
    [MaxStockLevel] INT DEFAULT 1000,
    [ReorderLevel] INT DEFAULT 20,
    [PrescriptionRequired] BIT NOT NULL DEFAULT 0,
    [Dosage] NVARCHAR(100),
    [DosageForm] NVARCHAR(50),
    [Strength] NVARCHAR(50),
    [PackSize] INT DEFAULT 1,
    [NDCNumber] NVARCHAR(50),
    [LotNumber] NVARCHAR(50),
    [ExpiryDate] DATE,
    [ManufactureDate] DATE,
    [IsFeatured] BIT NOT NULL DEFAULT 0,
    [IsNewArrival] BIT NOT NULL DEFAULT 0,
    [IsBestSeller] BIT NOT NULL DEFAULT 0,
    [AverageRating] DECIMAL(3,2) DEFAULT 0,
    [ReviewCount] INT DEFAULT 0,
    [PurchaseCount] INT DEFAULT 0,
    [ImageUrl] NVARCHAR(255),
    [ThumbnailUrl] NVARCHAR(255),
    [SEOTitle] NVARCHAR(200),
    [SEODescription] NVARCHAR(500),
    [SEOKeywords] NVARCHAR(500),
    [IsActive] BIT NOT NULL DEFAULT 1,
    [CreatedDate] DATETIME NOT NULL DEFAULT GETDATE(),
    [ModifiedDate] DATETIME,
    [CreatedBy] INT,
    [ModifiedBy] INT,
    FOREIGN KEY ([CategoryId]) REFERENCES [Categories]([CategoryId]),
    FOREIGN KEY ([BrandId]) REFERENCES [Brands]([BrandId]),
    FOREIGN KEY ([CreatedBy]) REFERENCES [Users]([UserId]),
    FOREIGN KEY ([ModifiedBy]) REFERENCES [Users]([UserId])
);

-- 5. ORDERS TABLE
CREATE TABLE [Orders] (
    [OrderId] INT IDENTITY(1,1) PRIMARY KEY,
    [OrderNumber] NVARCHAR(50) NOT NULL UNIQUE,
    [CustomerId] INT NOT NULL,
    [Status] NVARCHAR(20) NOT NULL DEFAULT 'Pending',
    [OrderDate] DATETIME NOT NULL DEFAULT GETDATE(),
    [ExpectedDeliveryDate] DATETIME,
    [ActualDeliveryDate] DATETIME,
    [Subtotal] DECIMAL(10,2) NOT NULL,
    [TaxAmount] DECIMAL(10,2) DEFAULT 0,
    [ShippingCost] DECIMAL(10,2) DEFAULT 0,
    [DiscountAmount] DECIMAL(10,2) DEFAULT 0,
    [TotalAmount] DECIMAL(10,2) NOT NULL,
    [PaymentMethod] NVARCHAR(50),
    [PaymentStatus] NVARCHAR(20) DEFAULT 'Pending',
    [PaymentReference] NVARCHAR(100),
    [PaymentTransactionId] NVARCHAR(100),
    [PaymentDate] DATETIME,
    [InvoiceGenerated] BIT DEFAULT 0,
    [InvoiceDate] DATETIME,
    [InvoiceNumber] NVARCHAR(50),
    [ShippingAddress] NVARCHAR(500) NOT NULL,
    [ShippingCity] NVARCHAR(100),
    [ShippingState] NVARCHAR(100),
    [ShippingPostalCode] NVARCHAR(20),
    [ShippingCountry] NVARCHAR(100),
    [ContactPhone] NVARCHAR(20),
    [ContactEmail] NVARCHAR(100),
    [TrackingNumber] NVARCHAR(100),
    [CourierService] NVARCHAR(100),
    [DeliveryInstructions] NVARCHAR(500),
    [RequiresPrescription] BIT DEFAULT 0,
    [PrescriptionVerified] BIT DEFAULT 0,
    [VerifiedBy] INT,
    [VerificationDate] DATETIME,
    [VerificationNotes] NVARCHAR(500),
    [IsUrgent] BIT DEFAULT 0,
    [IsGift] BIT DEFAULT 0,
    [Notes] NVARCHAR(1000),
    [CreatedDate] DATETIME NOT NULL DEFAULT GETDATE(),
    [ModifiedDate] DATETIME,
    [CreatedBy] INT,
    [ModifiedBy] INT,
    FOREIGN KEY ([CustomerId]) REFERENCES [Users]([UserId]),
    FOREIGN KEY ([VerifiedBy]) REFERENCES [Users]([UserId]),
    FOREIGN KEY ([CreatedBy]) REFERENCES [Users]([UserId]),
    FOREIGN KEY ([ModifiedBy]) REFERENCES [Users]([UserId])
);

-- 6. ORDER ITEMS TABLE
CREATE TABLE [OrderItems] (
    [OrderItemId] INT IDENTITY(1,1) PRIMARY KEY,
    [OrderId] INT NOT NULL,
    [MedicineId] INT NOT NULL,
    [Quantity] INT NOT NULL,
    [UnitPrice] DECIMAL(10,2) NOT NULL,
    [DiscountAmount] DECIMAL(10,2) DEFAULT 0,
    [TotalPrice] DECIMAL(10,2) NOT NULL,
    [SpecialInstructions] NVARCHAR(500),
    [CreatedDate] DATETIME NOT NULL DEFAULT GETDATE(),
    FOREIGN KEY ([OrderId]) REFERENCES [Orders]([OrderId]) ON DELETE CASCADE,
    FOREIGN KEY ([MedicineId]) REFERENCES [Medicines]([MedicineId])
);

-- 7. SHOPPING CART TABLE
CREATE TABLE [ShoppingCart] (
    [CartId] INT IDENTITY(1,1) PRIMARY KEY,
    [UserId] INT NOT NULL,
    [MedicineId] INT NOT NULL,
    [Quantity] INT NOT NULL,
    [AddedDate] DATETIME NOT NULL DEFAULT GETDATE(),
    [ModifiedDate] DATETIME,
    FOREIGN KEY ([UserId]) REFERENCES [Users]([UserId]) ON DELETE CASCADE,
    FOREIGN KEY ([MedicineId]) REFERENCES [Medicines]([MedicineId]),
    UNIQUE ([UserId], [MedicineId])
);

-- 8. PRESCRIPTIONS TABLE
CREATE TABLE [Prescriptions] (
    [PrescriptionId] INT IDENTITY(1,1) PRIMARY KEY,
    [UserId] INT NOT NULL,
    [PrescriptionNumber] NVARCHAR(50) NOT NULL UNIQUE,
    [DoctorName] NVARCHAR(100) NOT NULL,
    [DoctorLicense] NVARCHAR(50),
    [DoctorPhone] NVARCHAR(20),
    [DoctorEmail] NVARCHAR(100),
    [ClinicName] NVARCHAR(200),
    [ClinicAddress] NVARCHAR(500),
    [PrescriptionDate] DATE NOT NULL,
    [ExpiryDate] DATE,
    [Diagnosis] NVARCHAR(500),
    [Instructions] NVARCHAR(1000),
    [OriginalFileName] NVARCHAR(255),
    [FilePath] NVARCHAR(500),
    [FileSize] BIGINT,
    [FileType] NVARCHAR(50),
    [AIProcessed] BIT DEFAULT 0,
    [AIExtractedText] NVARCHAR(MAX),
    [AIConfidenceScore] DECIMAL(5,2),
    [AIProcessingDate] DATETIME,
    [Status] NVARCHAR(20) DEFAULT 'Pending',
    [VerifiedBy] INT,
    [VerificationDate] DATETIME,
    [VerificationNotes] NVARCHAR(1000),
    [RejectionReason] NVARCHAR(500),
    [CreatedDate] DATETIME NOT NULL DEFAULT GETDATE(),
    [ModifiedDate] DATETIME,
    FOREIGN KEY ([UserId]) REFERENCES [Users]([UserId]),
    FOREIGN KEY ([VerifiedBy]) REFERENCES [Users]([UserId])
);

-- 9. PRESCRIPTION ITEMS TABLE
CREATE TABLE [PrescriptionItems] (
    [PrescriptionItemId] INT IDENTITY(1,1) PRIMARY KEY,
    [PrescriptionId] INT NOT NULL,
    [MedicineId] INT,
    [MedicineName] NVARCHAR(200) NOT NULL,
    [Dosage] NVARCHAR(100),
    [Frequency] NVARCHAR(100),
    [Duration] NVARCHAR(100),
    [Quantity] INT,
    [Instructions] NVARCHAR(500),
    [IsDispensed] BIT DEFAULT 0,
    [DispensedDate] DATETIME,
    [DispensedBy] INT,
    FOREIGN KEY ([PrescriptionId]) REFERENCES [Prescriptions]([PrescriptionId]) ON DELETE CASCADE,
    FOREIGN KEY ([MedicineId]) REFERENCES [Medicines]([MedicineId]),
    FOREIGN KEY ([DispensedBy]) REFERENCES [Users]([UserId])
);

-- 10. FAMILY PROFILES TABLE
CREATE TABLE [FamilyProfiles] (
    [FamilyProfileId] INT IDENTITY(1,1) PRIMARY KEY,
    [UserId] INT NOT NULL,
    [MemberName] NVARCHAR(100) NOT NULL,
    [Relationship] NVARCHAR(50),
    [DateOfBirth] DATE,
    [Gender] NVARCHAR(10),
    [BloodGroup] NVARCHAR(10),
    [Allergies] NVARCHAR(1000),
    [MedicalConditions] NVARCHAR(1000),
    [EmergencyContact] NVARCHAR(100),
    [EmergencyPhone] NVARCHAR(20),
    [IsActive] BIT DEFAULT 1,
    [CreatedDate] DATETIME NOT NULL DEFAULT GETDATE(),
    FOREIGN KEY ([UserId]) REFERENCES [Users]([UserId]) ON DELETE CASCADE
);

-- 11. HEALTH REMINDERS TABLE
CREATE TABLE [HealthReminders] (
    [ReminderId] INT IDENTITY(1,1) PRIMARY KEY,
    [UserId] INT NOT NULL,
    [FamilyProfileId] INT,
    [Title] NVARCHAR(200) NOT NULL,
    [Description] NVARCHAR(1000),
    [ReminderType] NVARCHAR(50),
    [NextReminderDate] DATETIME NOT NULL,
    [Frequency] NVARCHAR(50),
    [FrequencyValue] INT DEFAULT 1,
    [EndDate] DATETIME,
    [IsActive] BIT DEFAULT 1,
    [IsCompleted] BIT DEFAULT 0,
    [CompletedDate] DATETIME,
    [SnoozeCount] INT DEFAULT 0,
    [LastSnoozedDate] DATETIME,
    [CreatedDate] DATETIME NOT NULL DEFAULT GETDATE(),
    FOREIGN KEY ([UserId]) REFERENCES [Users]([UserId]) ON DELETE CASCADE,
    FOREIGN KEY ([FamilyProfileId]) REFERENCES [FamilyProfiles]([FamilyProfileId])
);

-- 12. MEDICINE REVIEWS TABLE
CREATE TABLE [MedicineReviews] (
    [ReviewId] INT IDENTITY(1,1) PRIMARY KEY,
    [MedicineId] INT NOT NULL,
    [UserId] INT NOT NULL,
    [OrderId] INT,
    [Rating] INT NOT NULL CHECK ([Rating] >= 1 AND [Rating] <= 5),
    [Title] NVARCHAR(200),
    [ReviewText] NVARCHAR(2000),
    [IsVerifiedPurchase] BIT DEFAULT 0,
    [IsApproved] BIT DEFAULT 0,
    [ApprovedBy] INT,
    [ApprovedDate] DATETIME,
    [HelpfulVotes] INT DEFAULT 0,
    [CreatedDate] DATETIME NOT NULL DEFAULT GETDATE(),
    [ModifiedDate] DATETIME,
    FOREIGN KEY ([MedicineId]) REFERENCES [Medicines]([MedicineId]),
    FOREIGN KEY ([UserId]) REFERENCES [Users]([UserId]),
    FOREIGN KEY ([OrderId]) REFERENCES [Orders]([OrderId]),
    FOREIGN KEY ([ApprovedBy]) REFERENCES [Users]([UserId])
);

-- 13. LOYALTY TRANSACTIONS TABLE
CREATE TABLE [LoyaltyTransactions] (
    [TransactionId] INT IDENTITY(1,1) PRIMARY KEY,
    [UserId] INT NOT NULL,
    [OrderId] INT,
    [TransactionType] NVARCHAR(20) NOT NULL,
    [Points] INT NOT NULL,
    [Description] NVARCHAR(500),
    [ExpiryDate] DATETIME,
    [CreatedDate] DATETIME NOT NULL DEFAULT GETDATE(),
    FOREIGN KEY ([UserId]) REFERENCES [Users]([UserId]),
    FOREIGN KEY ([OrderId]) REFERENCES [Orders]([OrderId])
);

-- 14. OFFERS TABLE
CREATE TABLE [Offers] (
    [OfferId] INT IDENTITY(1,1) PRIMARY KEY,
    [Code] NVARCHAR(50) NOT NULL UNIQUE,
    [Title] NVARCHAR(200) NOT NULL,
    [Description] NVARCHAR(1000),
    [OfferType] NVARCHAR(20) NOT NULL,
    [DiscountValue] DECIMAL(10,2) NOT NULL,
    [MinOrderAmount] DECIMAL(10,2) DEFAULT 0,
    [MaxDiscountAmount] DECIMAL(10,2),
    [UsageLimit] INT,
    [UsageCount] INT DEFAULT 0,
    [UserUsageLimit] INT DEFAULT 1,
    [StartDate] DATETIME NOT NULL,
    [EndDate] DATETIME NOT NULL,
    [IsActive] BIT DEFAULT 1,
    [ApplicableCategories] NVARCHAR(500),
    [ApplicableMedicines] NVARCHAR(500),
    [CreatedDate] DATETIME NOT NULL DEFAULT GETDATE(),
    [CreatedBy] INT,
    FOREIGN KEY ([CreatedBy]) REFERENCES [Users]([UserId])
);

-- 15. USER OFFER USAGE TABLE
CREATE TABLE [UserOfferUsage] (
    [UsageId] INT IDENTITY(1,1) PRIMARY KEY,
    [UserId] INT NOT NULL,
    [OfferId] INT NOT NULL,
    [OrderId] INT NOT NULL,
    [DiscountAmount] DECIMAL(10,2) NOT NULL,
    [UsedDate] DATETIME NOT NULL DEFAULT GETDATE(),
    FOREIGN KEY ([UserId]) REFERENCES [Users]([UserId]),
    FOREIGN KEY ([OfferId]) REFERENCES [Offers]([OfferId]),
    FOREIGN KEY ([OrderId]) REFERENCES [Orders]([OrderId])
);

-- 16. NOTIFICATIONS TABLE
CREATE TABLE [Notifications] (
    [NotificationId] INT IDENTITY(1,1) PRIMARY KEY,
    [UserId] INT NOT NULL,
    [Title] NVARCHAR(200) NOT NULL,
    [Message] NVARCHAR(1000) NOT NULL,
    [NotificationType] NVARCHAR(50),
    [RelatedEntityType] NVARCHAR(50),
    [RelatedEntityId] INT,
    [IsRead] BIT DEFAULT 0,
    [ReadDate] DATETIME,
    [Priority] NVARCHAR(20) DEFAULT 'Normal',
    [ExpiryDate] DATETIME,
    [ActionUrl] NVARCHAR(500),
    [CreatedDate] DATETIME NOT NULL DEFAULT GETDATE(),
    FOREIGN KEY ([UserId]) REFERENCES [Users]([UserId]) ON DELETE CASCADE
);

-- 17. CHAT SESSIONS TABLE
CREATE TABLE [ChatSessions] (
    [SessionId] INT IDENTITY(1,1) PRIMARY KEY,
    [UserId] INT,
    [SessionToken] NVARCHAR(255) NOT NULL UNIQUE,
    [StartDate] DATETIME NOT NULL DEFAULT GETDATE(),
    [EndDate] DATETIME,
    [IsActive] BIT DEFAULT 1,
    [UserAgent] NVARCHAR(500),
    [IPAddress] NVARCHAR(45),
    FOREIGN KEY ([UserId]) REFERENCES [Users]([UserId])
);

-- 18. CHAT MESSAGES TABLE
CREATE TABLE [ChatMessages] (
    [MessageId] INT IDENTITY(1,1) PRIMARY KEY,
    [SessionId] INT NOT NULL,
    [MessageType] NVARCHAR(20) NOT NULL,
    [Message] NVARCHAR(MAX) NOT NULL,
    [Timestamp] DATETIME NOT NULL DEFAULT GETDATE(),
    [AIModel] NVARCHAR(100),
    [ResponseTime] INT,
    [TokensUsed] INT,
    [ConfidenceScore] DECIMAL(5,2),
    FOREIGN KEY ([SessionId]) REFERENCES [ChatSessions]([SessionId]) ON DELETE CASCADE
);

-- 19. AI RECOMMENDATIONS TABLE
CREATE TABLE [AIRecommendations] (
    [RecommendationId] INT IDENTITY(1,1) PRIMARY KEY,
    [UserId] INT,
    [SessionId] INT,
    [RecommendationType] NVARCHAR(50),
    [MedicineId] INT,
    [RecommendationText] NVARCHAR(2000),
    [ConfidenceScore] DECIMAL(5,2),
    [Reasoning] NVARCHAR(1000),
    [IsAccepted] BIT,
    [UserFeedback] NVARCHAR(500),
    [CreatedDate] DATETIME NOT NULL DEFAULT GETDATE(),
    FOREIGN KEY ([UserId]) REFERENCES [Users]([UserId]),
    FOREIGN KEY ([SessionId]) REFERENCES [ChatSessions]([SessionId]),
    FOREIGN KEY ([MedicineId]) REFERENCES [Medicines]([MedicineId])
);

-- 20. SYSTEM SETTINGS TABLE
CREATE TABLE [SystemSettings] (
    [SettingId] INT IDENTITY(1,1) PRIMARY KEY,
    [SettingKey] NVARCHAR(100) NOT NULL UNIQUE,
    [SettingValue] NVARCHAR(2000),
    [Description] NVARCHAR(500),
    [DataType] NVARCHAR(20) DEFAULT 'String',
    [Category] NVARCHAR(50),
    [IsEditable] BIT DEFAULT 1,
    [ModifiedDate] DATETIME,
    [ModifiedBy] INT,
    FOREIGN KEY ([ModifiedBy]) REFERENCES [Users]([UserId])
);

-- 21. AUDIT LOGS TABLE
CREATE TABLE [AuditLogs] (
    [LogId] INT IDENTITY(1,1) PRIMARY KEY,
    [UserId] INT,
    [Action] NVARCHAR(100) NOT NULL,
    [EntityType] NVARCHAR(50),
    [EntityId] INT,
    [OldValues] NVARCHAR(MAX),
    [NewValues] NVARCHAR(MAX),
    [IPAddress] NVARCHAR(45),
    [UserAgent] NVARCHAR(500),
    [Timestamp] DATETIME NOT NULL DEFAULT GETDATE(),
    FOREIGN KEY ([UserId]) REFERENCES [Users]([UserId])
);

-- 22. ERROR LOGS TABLE
CREATE TABLE [ErrorLogs] (
    [ErrorId] INT IDENTITY(1,1) PRIMARY KEY,
    [ErrorMessage] NVARCHAR(2000) NOT NULL,
    [StackTrace] NVARCHAR(MAX),
    [Source] NVARCHAR(200),
    [UserId] INT,
    [RequestUrl] NVARCHAR(500),
    [HttpMethod] NVARCHAR(10),
    [IPAddress] NVARCHAR(45),
    [UserAgent] NVARCHAR(500),
    [Severity] NVARCHAR(20) DEFAULT 'Error',
    [CreatedDate] DATETIME NOT NULL DEFAULT GETDATE(),
    FOREIGN KEY ([UserId]) REFERENCES [Users]([UserId])
);

-- 23. MEDICINE IMAGES TABLE
CREATE TABLE [MedicineImages] (
    [ImageId] INT IDENTITY(1,1) PRIMARY KEY,
    [MedicineId] INT NOT NULL,
    [ImageUrl] NVARCHAR(255) NOT NULL,
    [AltText] NVARCHAR(200),
    [IsPrimary] BIT NOT NULL DEFAULT 0,
    [DisplayOrder] INT DEFAULT 0,
    [CreatedDate] DATETIME NOT NULL DEFAULT GETDATE(),
    FOREIGN KEY ([MedicineId]) REFERENCES [Medicines]([MedicineId]) ON DELETE CASCADE
);

-- 24. STOCK MOVEMENTS TABLE
CREATE TABLE [StockMovements] (
    [MovementId] INT IDENTITY(1,1) PRIMARY KEY,
    [MedicineId] INT NOT NULL,
    [MovementType] NVARCHAR(20) NOT NULL,
    [Quantity] INT NOT NULL,
    [PreviousStock] INT NOT NULL,
    [NewStock] INT NOT NULL,
    [UnitCost] DECIMAL(10,2),
    [TotalCost] DECIMAL(10,2),
    [Reference] NVARCHAR(100),
    [Notes] NVARCHAR(500),
    [CreatedDate] DATETIME NOT NULL DEFAULT GETDATE(),
    [CreatedBy] INT,
    FOREIGN KEY ([MedicineId]) REFERENCES [Medicines]([MedicineId]),
    FOREIGN KEY ([CreatedBy]) REFERENCES [Users]([UserId])
);

-- 25. MEDICINE INTERACTIONS TABLE
CREATE TABLE [MedicineInteractions] (
    [InteractionId] INT IDENTITY(1,1) PRIMARY KEY,
    [MedicineId1] INT NOT NULL,
    [MedicineId2] INT NOT NULL,
    [InteractionType] NVARCHAR(50),
    [Description] NVARCHAR(1000),
    [Severity] NVARCHAR(20),
    [CreatedDate] DATETIME NOT NULL DEFAULT GETDATE(),
    FOREIGN KEY ([MedicineId1]) REFERENCES [Medicines]([MedicineId]),
    FOREIGN KEY ([MedicineId2]) REFERENCES [Medicines]([MedicineId])
);

-- =============================================
-- INDEXES FOR PERFORMANCE
-- =============================================

-- Users Table Indexes
CREATE INDEX IX_Users_Email ON [Users]([Email]);
CREATE INDEX IX_Users_Role ON [Users]([Role]);
CREATE INDEX IX_Users_IsActive ON [Users]([IsActive]);

-- Medicines Table Indexes
CREATE INDEX IX_Medicines_Category ON [Medicines]([CategoryId]);
CREATE INDEX IX_Medicines_Brand ON [Medicines]([BrandId]);
CREATE INDEX IX_Medicines_Price ON [Medicines]([Price]);
CREATE INDEX IX_Medicines_Stock ON [Medicines]([StockQuantity]);
CREATE INDEX IX_Medicines_Featured ON [Medicines]([IsFeatured]);
CREATE INDEX IX_Medicines_Active ON [Medicines]([IsActive]);

-- Orders Table Indexes
CREATE INDEX IX_Orders_Customer ON [Orders]([CustomerId]);
CREATE INDEX IX_Orders_Status ON [Orders]([Status]);
CREATE INDEX IX_Orders_Date ON [Orders]([OrderDate]);
CREATE INDEX IX_Orders_PaymentStatus ON [Orders]([PaymentStatus]);

-- Order Items Table Indexes
CREATE INDEX IX_OrderItems_Order ON [OrderItems]([OrderId]);
CREATE INDEX IX_OrderItems_Medicine ON [OrderItems]([MedicineId]);

-- Shopping Cart Table Indexes
CREATE INDEX IX_ShoppingCart_User ON [ShoppingCart]([UserId]);

-- Prescriptions Table Indexes
CREATE INDEX IX_Prescriptions_User ON [Prescriptions]([UserId]);
CREATE INDEX IX_Prescriptions_Status ON [Prescriptions]([Status]);
CREATE INDEX IX_Prescriptions_Date ON [Prescriptions]([PrescriptionDate]);

-- Family Profiles Table Indexes
CREATE INDEX IX_FamilyProfiles_User ON [FamilyProfiles]([UserId]);

-- Health Reminders Table Indexes
CREATE INDEX IX_HealthReminders_User ON [HealthReminders]([UserId]);
CREATE INDEX IX_HealthReminders_Date ON [HealthReminders]([NextReminderDate]);
CREATE INDEX IX_HealthReminders_Active ON [HealthReminders]([IsActive]);

-- Medicine Reviews Table Indexes
CREATE INDEX IX_MedicineReviews_Medicine ON [MedicineReviews]([MedicineId]);
CREATE INDEX IX_MedicineReviews_User ON [MedicineReviews]([UserId]);
CREATE INDEX IX_MedicineReviews_Rating ON [MedicineReviews]([Rating]);

-- Loyalty Transactions Table Indexes
CREATE INDEX IX_LoyaltyTransactions_User ON [LoyaltyTransactions]([UserId]);
CREATE INDEX IX_LoyaltyTransactions_Type ON [LoyaltyTransactions]([TransactionType]);

-- Offers Table Indexes
CREATE INDEX IX_Offers_Code ON [Offers]([Code]);
CREATE INDEX IX_Offers_Active ON [Offers]([IsActive]);
CREATE INDEX IX_Offers_Dates ON [Offers]([StartDate], [EndDate]);

-- Notifications Table Indexes
CREATE INDEX IX_Notifications_User ON [Notifications]([UserId]);
CREATE INDEX IX_Notifications_Read ON [Notifications]([IsRead]);
CREATE INDEX IX_Notifications_Type ON [Notifications]([NotificationType]);

-- Chat Sessions Table Indexes
CREATE INDEX IX_ChatSessions_User ON [ChatSessions]([UserId]);
CREATE INDEX IX_ChatSessions_Token ON [ChatSessions]([SessionToken]);

-- Chat Messages Table Indexes
CREATE INDEX IX_ChatMessages_Session ON [ChatMessages]([SessionId]);
CREATE INDEX IX_ChatMessages_Type ON [ChatMessages]([MessageType]);
CREATE INDEX IX_ChatMessages_Timestamp ON [ChatMessages]([Timestamp]);

-- Stock Movements Table Indexes
CREATE INDEX IX_StockMovements_Medicine ON [StockMovements]([MedicineId]);
CREATE INDEX IX_StockMovements_Type ON [StockMovements]([MovementType]);
CREATE INDEX IX_StockMovements_Date ON [StockMovements]([CreatedDate]);

-- System Settings Table Indexes
CREATE INDEX IX_SystemSettings_Key ON [SystemSettings]([SettingKey]);
CREATE INDEX IX_SystemSettings_Category ON [SystemSettings]([Category]);

-- Audit Logs Table Indexes
CREATE INDEX IX_AuditLogs_User ON [AuditLogs]([UserId]);
CREATE INDEX IX_AuditLogs_Action ON [AuditLogs]([Action]);
CREATE INDEX IX_AuditLogs_Entity ON [AuditLogs]([EntityType], [EntityId]);
CREATE INDEX IX_AuditLogs_Timestamp ON [AuditLogs]([Timestamp]);

-- Error Logs Table Indexes
CREATE INDEX IX_ErrorLogs_Severity ON [ErrorLogs]([Severity]);
CREATE INDEX IX_ErrorLogs_Date ON [ErrorLogs]([CreatedDate]);
CREATE INDEX IX_ErrorLogs_User ON [ErrorLogs]([UserId]);

-- =============================================
-- SAMPLE DATA FOR TESTING
-- =============================================

-- Insert Categories
INSERT INTO [Categories] ([Name], [Description]) VALUES
('Pain Relief', 'Medications for pain management and relief'),
('Antibiotics', 'Prescription antibiotics for bacterial infections'),
('Vitamins & Supplements', 'Nutritional supplements and vitamins'),
('Cold & Flu', 'Medications for cold and flu symptoms'),
('Digestive Health', 'Medications for digestive and stomach issues'),
('Heart & Blood Pressure', 'Cardiovascular medications'),
('Diabetes Care', 'Medications and supplies for diabetes management'),
('Skin Care', 'Topical medications and skin treatments'),
('Eye Care', 'Eye drops and vision care products'),
('First Aid', 'Emergency and first aid supplies');

-- Insert Brands
INSERT INTO [Brands] ([Name], [Description]) VALUES
('Generic', 'Generic pharmaceutical products'),
('Pfizer', 'Leading pharmaceutical company'),
('Johnson & Johnson', 'Healthcare and pharmaceutical products'),
('Bayer', 'German pharmaceutical and life sciences company'),
('GSK', 'GlaxoSmithKline pharmaceutical products'),
('Novartis', 'Swiss multinational pharmaceutical company'),
('Merck', 'American multinational pharmaceutical company'),
('Abbott', 'Healthcare and pharmaceutical products'),
('Roche', 'Swiss multinational healthcare company'),
('Sanofi', 'French multinational pharmaceutical company');

-- Insert Sample Medicines
INSERT INTO [Medicines] ([Name], [GenericName], [Description], [CategoryId], [BrandId], [Price], [DiscountPercentage], [StockQuantity], [PrescriptionRequired], [Dosage], [DosageForm], [Strength], [IsFeatured], [IsActive]) VALUES
('Tylenol Extra Strength', 'Acetaminophen', 'Fast-acting pain relief for headaches, muscle aches, and fever', 1, 3, 12.99, 10, 150, 0, '500mg', 'Tablet', '500mg', 1, 1),
('Advil Liqui-Gels', 'Ibuprofen', 'Fast pain relief in liquid-filled capsules', 1, 3, 15.49, 15, 200, 0, '200mg', 'Capsule', '200mg', 1, 1),
('Amoxicillin', 'Amoxicillin', 'Broad-spectrum antibiotic for bacterial infections', 2, 1, 25.99, 0, 75, 1, '500mg', 'Capsule', '500mg', 0, 1),
('Vitamin D3', 'Cholecalciferol', 'Essential vitamin for bone health and immune support', 3, 1, 18.99, 20, 300, 0, '1000 IU', 'Tablet', '1000 IU', 1, 1),
('Robitussin DM', 'Dextromethorphan', 'Cough suppressant and expectorant', 4, 3, 11.99, 5, 120, 0, '15mg/5ml', 'Syrup', '15mg/5ml', 0, 1),
('Pepto-Bismol', 'Bismuth Subsalicylate', 'Relief from upset stomach, nausea, and diarrhea', 5, 3, 9.99, 0, 180, 0, '262mg', 'Tablet', '262mg', 0, 1),
('Lisinopril', 'Lisinopril', 'ACE inhibitor for high blood pressure', 6, 1, 32.50, 0, 90, 1, '10mg', 'Tablet', '10mg', 0, 1),
('Metformin', 'Metformin HCl', 'Type 2 diabetes medication', 7, 1, 28.75, 0, 100, 1, '500mg', 'Tablet', '500mg', 0, 1),
('Hydrocortisone Cream', 'Hydrocortisone', 'Topical anti-inflammatory for skin irritation', 8, 1, 7.99, 0, 250, 0, '1%', 'Cream', '1%', 0, 1),
('Artificial Tears', 'Polyethylene Glycol', 'Lubricating eye drops for dry eyes', 9, 1, 6.99, 10, 200, 0, '0.4%', 'Drops', '0.4%', 0, 1);

-- Insert System Settings
INSERT INTO [SystemSettings] ([SettingKey], [SettingValue], [Description], [Category], [IsEditable]) VALUES
('SiteName', 'MediEase', 'Name of the pharmacy system', 'General', 1),
('SiteEmail', '<EMAIL>', 'Primary contact email', 'General', 1),
('SitePhone', '******-0123', 'Primary contact phone number', 'General', 1),
('Currency', 'USD', 'Default currency for pricing', 'General', 1),
('TaxRate', '8.5', 'Default tax rate percentage', 'Financial', 1),
('ShippingCost', '5.99', 'Standard shipping cost', 'Financial', 1),
('FreeShippingThreshold', '50.00', 'Minimum order for free shipping', 'Financial', 1),
('LoyaltyPointsRate', '1', 'Points earned per dollar spent', 'Loyalty', 1),
('LoyaltyRedemptionRate', '100', 'Points needed for $1 discount', 'Loyalty', 1),
('MaxFileUploadSize', '5242880', 'Maximum file upload size in bytes (5MB)', 'System', 1),
('SessionTimeout', '30', 'Session timeout in minutes', 'Security', 1),
('PasswordMinLength', '8', 'Minimum password length', 'Security', 1),
('EnableEmailNotifications', 'true', 'Enable email notifications', 'Notifications', 1),
('EnableSMSNotifications', 'false', 'Enable SMS notifications', 'Notifications', 1),
('AIEnabled', 'true', 'Enable AI features', 'AI', 1),
('ChatbotEnabled', 'true', 'Enable chatbot functionality', 'AI', 1),
('PrescriptionVerificationRequired', 'true', 'Require prescription verification', 'Medical', 1),
('AutoApproveReviews', 'false', 'Automatically approve product reviews', 'Reviews', 1),
('MaintenanceMode', 'false', 'Enable maintenance mode', 'System', 1),
('BackupFrequency', 'Daily', 'Database backup frequency', 'System', 1);

-- Insert Initial Admin User (CHANGE PASSWORD BEFORE PRODUCTION!)
INSERT INTO [Users] ([Email], [PasswordHash], [FirstName], [LastName], [PhoneNumber], [Role], [IsActive], [IsEmailVerified], [Address], [City], [State], [PostalCode], [Country]) VALUES
('<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'System', 'Administrator', '******-0001', 'Admin', 1, 1, '123 Admin Street', 'Admin City', 'CA', '12345', 'USA');
